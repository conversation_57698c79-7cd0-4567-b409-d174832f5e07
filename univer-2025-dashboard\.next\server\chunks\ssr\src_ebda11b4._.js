module.exports = [
"[project]/src/components/BreederChart.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>__TURBOPACK__default__export__
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts/highcharts.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/highcharts-react-official/dist/highcharts-react.min.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/ThemeProvider.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const BreederChart = ({ title, varieties, breederColor, breederName, allVarietiesData = [] })=>{
    const chartRef = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].useRef(null);
    const { theme } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$ThemeProvider$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useTheme"])();
    // Modulok betöltése komponens betöltéskor (egyszerűsített)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }, []);
    // Dinamikus színek a téma alapján - külön színek sötét és világos módhoz
    const themeColors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMemo"])(()=>{
        if (theme === 'dark') {
            // SÖTÉT MÓD - Világos színek sötét háttéren
            return {
                background: 'transparent',
                titleColor: '#f8fafc',
                subtitleColor: '#cbd5e1',
                labelColor: '#94a3b8',
                gridLineColor: '#475569',
                lineColor: '#475569',
                crosshairColor: 'rgba(248, 250, 252, 0.4)',
                plotBandColor: 'rgba(248, 250, 252, 0.08)',
                plotBandColorAlt: 'rgba(248, 250, 252, 0.15)',
                tooltipBg: 'rgba(15, 23, 42, 0.95)',
                tooltipBorder: '#475569',
                tooltipText: '#f8fafc',
                exportButtonBg: 'rgba(51, 65, 85, 0.9)',
                exportButtonHover: 'rgba(71, 85, 105, 0.95)',
                exportButtonStroke: '#f8fafc'
            };
        } else {
            // VILÁGOS MÓD - Sötét színek világos háttéren
            return {
                background: 'transparent',
                titleColor: '#0f172a',
                subtitleColor: '#334155',
                labelColor: '#64748b',
                gridLineColor: '#e2e8f0',
                lineColor: '#e2e8f0',
                crosshairColor: 'rgba(15, 23, 42, 0.4)',
                plotBandColor: 'rgba(15, 23, 42, 0.06)',
                plotBandColorAlt: 'rgba(15, 23, 42, 0.12)',
                tooltipBg: 'rgba(255, 255, 255, 0.95)',
                tooltipBorder: '#e2e8f0',
                tooltipText: '#0f172a',
                exportButtonBg: 'rgba(248, 250, 252, 0.9)',
                exportButtonHover: 'rgba(226, 232, 240, 0.95)',
                exportButtonStroke: '#0f172a'
            };
        }
    }, [
        theme
    ]);
    // Színárnyalatok generálása a fajtákhoz
    const generateColorShades = (baseColor, count)=>{
        const colors = [];
        for(let i = 0; i < count; i++){
            // Ha WALLER fajtáról van szó, akkor zöld színt használunk
            if (varieties[i]?.variety === 'WALLER') {
                colors.push('#16a34a'); // Zöld szín a WALLER fajtának
            } else {
                const factor = 0.3 + i * 0.7 / Math.max(count - 1, 1);
                colors.push(adjustColorBrightness(baseColor, factor));
            }
        }
        return colors;
    };
    const adjustColorBrightness = (hex, factor)=>{
        const num = parseInt(hex.replace('#', ''), 16);
        const R = Math.round((num >> 16) * factor);
        const G = Math.round((num >> 8 & 0x00FF) * factor);
        const B = Math.round((num & 0x0000FF) * factor);
        return '#' + (R << 16 | G << 8 | B).toString(16).padStart(6, '0');
    };
    const colors = generateColorShades(breederColor, varieties.length);
    // Adatok előkészítése Highcharts számára
    const categories = [
        'M-I',
        'M-II',
        'Cs-I',
        'Cs-II',
        'L-I',
        'L-II'
    ];
    const series = varieties.map((variety, index)=>({
            type: 'column',
            name: variety.variety,
            data: categories.map((location)=>variety.locations[location]),
            color: colors[index]
        }));
    const options = {
        chart: {
            type: 'column',
            backgroundColor: themeColors.background,
            style: {
                fontFamily: 'var(--font-geist-sans)'
            },
            animation: false
        },
        title: {
            text: `${breederName}`,
            style: {
                color: themeColors.titleColor,
                fontSize: '18px',
                fontWeight: '600'
            }
        },
        subtitle: {
            text: title,
            style: {
                color: themeColors.subtitleColor,
                fontSize: '14px'
            }
        },
        xAxis: {
            categories: categories,
            labels: {
                style: {
                    color: themeColors.labelColor
                }
            },
            lineColor: themeColors.lineColor,
            tickColor: themeColors.lineColor,
            crosshair: {
                width: 1,
                color: themeColors.crosshairColor,
                dashStyle: 'Solid'
            },
            plotBands: [
                {
                    from: -0.5,
                    to: 1.5,
                    color: themeColors.plotBandColor,
                    label: {
                        text: 'Mezőberény',
                        style: {
                            color: themeColors.labelColor,
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                },
                {
                    from: 1.5,
                    to: 3.5,
                    color: themeColors.plotBandColorAlt,
                    label: {
                        text: 'Csabacsűd',
                        style: {
                            color: themeColors.labelColor,
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                },
                {
                    from: 3.5,
                    to: 5.5,
                    color: themeColors.plotBandColor,
                    label: {
                        text: 'Lakitelek',
                        style: {
                            color: themeColors.labelColor,
                            fontSize: '12px'
                        },
                        align: 'center'
                    }
                }
            ]
        },
        yAxis: {
            title: {
                text: 't/ha',
                style: {
                    color: themeColors.labelColor
                }
            },
            labels: {
                style: {
                    color: themeColors.labelColor
                }
            },
            gridLineColor: themeColors.gridLineColor
        },
        legend: {
            enabled: true,
            itemStyle: {
                color: themeColors.labelColor
            },
            itemHoverStyle: {
                color: themeColors.titleColor
            }
        },
        plotOptions: {
            column: {
                animation: false,
                borderWidth: 0,
                borderRadius: 3,
                groupPadding: 0.1,
                pointPadding: 0.05,
                dataLabels: {
                    enabled: false
                },
                states: {
                    hover: {
                        brightness: 0.2,
                        borderColor: themeColors.titleColor,
                        borderWidth: 2
                    },
                    inactive: {
                        opacity: 0.3
                    }
                },
                cursor: 'pointer',
                point: {
                    events: {
                        mouseOver: function() {
                            const chart = this.series.chart;
                            const point = this;
                            const varietyName = point.series.name;
                            // Kiemeljük az összes ugyanolyan fajta oszlopot
                            chart.series.forEach((series)=>{
                                if (series.name === varietyName) {
                                    // Kiemeljük az aktív fajta oszlopait
                                    series.points.forEach((p)=>{
                                        p.update({
                                            color: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].color(series.color).brighten(0.2).get(),
                                            borderColor: '#ffffff',
                                            borderWidth: 2
                                        }, false);
                                    });
                                    series.update({
                                        opacity: 1
                                    }, false);
                                } else {
                                    // Elhalványítjuk a többi fajtát
                                    series.update({
                                        opacity: 0.3
                                    }, false);
                                    series.points.forEach((p)=>{
                                        p.update({
                                            opacity: 0.3
                                        }, false);
                                    });
                                }
                            });
                            chart.redraw();
                        },
                        mouseOut: function() {
                            const chart = this.series.chart;
                            // Visszaállítjuk az eredeti állapotot
                            chart.series.forEach((series)=>{
                                series.update({
                                    opacity: 1
                                }, false);
                                series.points.forEach((p)=>{
                                    p.update({
                                        color: series.color,
                                        borderColor: undefined,
                                        borderWidth: 0,
                                        opacity: 1
                                    }, false);
                                });
                            });
                            chart.redraw();
                        }
                    }
                },
                stickyTracking: true
            },
            series: {
                states: {
                    hover: {
                        enabled: true
                    },
                    inactive: {
                        opacity: 0.3
                    }
                }
            }
        },
        series: series,
        credits: {
            enabled: false
        },
        exporting: {
            enabled: true,
            buttons: {
                contextButton: {
                    enabled: true,
                    theme: {
                        fill: themeColors.exportButtonBg,
                        stroke: themeColors.exportButtonStroke,
                        r: 4,
                        states: {
                            hover: {
                                fill: themeColors.exportButtonHover,
                                stroke: themeColors.exportButtonStroke
                            },
                            select: {
                                fill: themeColors.exportButtonHover,
                                stroke: themeColors.exportButtonStroke
                            }
                        }
                    },
                    menuItems: [
                        'viewFullscreen',
                        'separator',
                        'downloadPNG',
                        'downloadJPEG',
                        'downloadSVG'
                    ],
                    x: -10,
                    y: 10
                }
            }
        },
        navigation: {
            buttonOptions: {
                enabled: true
            }
        },
        tooltip: {
            enabled: true,
            backgroundColor: themeColors.tooltipBg,
            borderColor: themeColors.tooltipBorder,
            borderRadius: 8,
            style: {
                color: themeColors.tooltipText,
                fontSize: '12px'
            },
            useHTML: true,
            positioner: function(labelWidth) {
                const chart = this.chart;
                const chartWidth = chart.chartWidth;
                // Tooltip a cím és a fullscreen gomb között, középen
                const x = chartWidth / 2 - labelWidth / 2; // Horizontálisan középre
                const y = 50; // A cím alatt, de a fullscreen gomb felett
                return {
                    x,
                    y
                };
            },
            formatter: function() {
                const point = this.point;
                const series = this.series;
                const chart = this.series.chart;
                // Megkeressük az összes ugyanolyan fajta adatait
                let varietyData = [];
                let totalValue = 0;
                let validCount = 0;
                chart.series.forEach((s)=>{
                    if (s.name === series.name) {
                        s.points.forEach((p)=>{
                            if (p.y !== null && p.y !== undefined && p.y > 0) {
                                varietyData.push({
                                    location: p.category,
                                    value: p.y,
                                    seriesName: s.name,
                                    color: s.color
                                });
                                totalValue += p.y;
                                validCount++;
                            }
                        });
                    }
                });
                // Átlag számítása
                const averageValue = validCount > 0 ? totalValue / validCount : 0;
                // Tooltip HTML összeállítása - téma-alapú színekkel
                let tooltipHtml = `<div style="width: 200px; max-height: 180px; display: flex; flex-direction: column; color: ${themeColors.tooltipText};">`;
                // Fejléc - téma-alapú színekkel
                const headerBorderColor = theme === 'dark' ? '#22c55e' : '#16a34a';
                tooltipHtml += `<div style="flex-shrink: 0; font-weight: bold; margin-bottom: 3px; font-size: 11px; color: ${themeColors.tooltipText}; border-bottom: 1px solid ${headerBorderColor}; padding-bottom: 2px;">${series.name}</div>`;
                // Scroll-ozható tartalom - kompakt
                tooltipHtml += `<div style="flex: 1; overflow-y: auto; max-height: 120px; margin-bottom: 3px;">`;
                // Összes helyszín adatai - téma-specifikus színekkel
                varietyData.forEach((data)=>{
                    const isCurrentPoint = data.location === point.category;
                    // Téma-specifikus színek
                    const bgColor = isCurrentPoint ? theme === 'dark' ? 'rgba(34, 197, 94, 0.15)' : 'rgba(22, 163, 74, 0.1)' : 'transparent';
                    const textColor = isCurrentPoint ? theme === 'dark' ? '#22c55e' : '#16a34a' : theme === 'dark' ? '#94a3b8' : '#64748b';
                    const valueColor = isCurrentPoint ? theme === 'dark' ? '#f8fafc' : '#0f172a' : theme === 'dark' ? '#cbd5e1' : '#475569';
                    const borderColor = isCurrentPoint ? theme === 'dark' ? '#22c55e' : '#16a34a' : 'transparent';
                    tooltipHtml += `<div style="display: flex; align-items: center; margin: 1px 0; padding: 1px 2px; border-radius: 2px; background: ${bgColor}; border-left: 1px solid ${borderColor};">`;
                    tooltipHtml += `<span style="width: 5px; height: 5px; background-color: ${data.color}; display: inline-block; margin-right: 3px; border-radius: 1px; flex-shrink: 0;"></span>`;
                    tooltipHtml += `<span style="flex: 1; font-size: 10px; color: ${textColor}; font-weight: ${isCurrentPoint ? '600' : '400'};">${data.location}</span>`;
                    tooltipHtml += `<span style="font-weight: bold; color: ${valueColor}; font-size: 10px; margin-left: 2px;">${data.value.toFixed(1)}</span>`;
                    tooltipHtml += `</div>`;
                });
                tooltipHtml += `</div>`;
                // Összeg - téma-specifikus színekkel
                const summaryBorderColor = theme === 'dark' ? 'rgba(248, 250, 252, 0.2)' : 'rgba(15, 23, 42, 0.2)';
                const summaryBgColor = theme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(22, 163, 74, 0.05)';
                const avgBgColor = theme === 'dark' ? 'rgba(248, 250, 252, 0.1)' : 'rgba(15, 23, 42, 0.05)';
                const avgColor = theme === 'dark' ? '#22c55e' : '#16a34a';
                const avgLabelColor = theme === 'dark' ? '#f8fafc' : '#0f172a';
                tooltipHtml += `<div style="flex-shrink: 0; border-top: 1px solid ${summaryBorderColor}; padding: 2px; background: ${summaryBgColor}; border-radius: 2px;">`;
                tooltipHtml += `<div style="display: flex; align-items: center; justify-content: space-between; font-weight: bold;">`;
                tooltipHtml += `<span style="font-size: 9px; color: ${avgLabelColor};">Átlag:</span>`;
                tooltipHtml += `<span style="font-size: 10px; color: ${avgColor}; background: ${avgBgColor}; padding: 1px 2px; border-radius: 2px;">${averageValue.toFixed(1)}</span>`;
                tooltipHtml += `</div>`;
                tooltipHtml += `</div>`;
                tooltipHtml += `</div>`;
                return tooltipHtml;
            }
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "w-full h-96 relative",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                onClick: ()=>{
                    if (chartRef.current) {
                        if (chartRef.current.requestFullscreen) {
                            chartRef.current.requestFullscreen();
                        } else if (chartRef.current.webkitRequestFullscreen) {
                            chartRef.current.webkitRequestFullscreen();
                        } else if (chartRef.current.mozRequestFullScreen) {
                            chartRef.current.mozRequestFullScreen();
                        } else if (chartRef.current.msRequestFullscreen) {
                            chartRef.current.msRequestFullscreen();
                        }
                    }
                },
                className: "absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg",
                title: "Teljes képernyő",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                    className: "w-5 h-5",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24",
                    xmlns: "http://www.w3.org/2000/svg",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                        strokeLinecap: "round",
                        strokeLinejoin: "round",
                        strokeWidth: 2,
                        d: "M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/BreederChart.tsx",
                        lineNumber: 485,
                        columnNumber: 11
                    }, ("TURBOPACK compile-time value", void 0))
                }, void 0, false, {
                    fileName: "[project]/src/components/BreederChart.tsx",
                    lineNumber: 478,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/BreederChart.tsx",
                lineNumber: 461,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0)),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                ref: chartRef,
                className: "w-full h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2d$react$2d$official$2f$dist$2f$highcharts$2d$react$2e$min$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                    highcharts: __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$highcharts$2f$highcharts$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"],
                    options: options
                }, void 0, false, {
                    fileName: "[project]/src/components/BreederChart.tsx",
                    lineNumber: 494,
                    columnNumber: 9
                }, ("TURBOPACK compile-time value", void 0))
            }, void 0, false, {
                fileName: "[project]/src/components/BreederChart.tsx",
                lineNumber: 493,
                columnNumber: 7
            }, ("TURBOPACK compile-time value", void 0))
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/BreederChart.tsx",
        lineNumber: 460,
        columnNumber: 5
    }, ("TURBOPACK compile-time value", void 0));
};
const __TURBOPACK__default__export__ = BreederChart;
}),
"[project]/src/data/raw_excel_data.json (json)", ((__turbopack_context__) => {

__turbopack_context__.v(JSON.parse("{\"Munka1\":[{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG11227*\",\"M-I.\":1.75,\"M-II.\":6.65,\"Cs-I.\":1.63,\"Cs-II.\":1.98,\"L-I.\":2.28,\"L-II.\":1.84},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG8492\",\"M-I.\":1.05,\"M-II.\":10.3,\"Cs-I.\":3.03,\"Cs-II.\":4.32,\"L-I.\":2.54,\"L-II.\":6.65},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG17219\",\"M-I.\":3.97,\"M-II.\":7.23,\"Cs-I.\":3.27,\"Cs-II.\":9.1,\"L-I.\":1.84,\"L-II.\":18.0},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG1578\",\"M-I.\":4.08,\"M-II.\":14.8,\"Cs-I.\":3.85,\"Cs-II.\":11.2,\"L-I.\":2.98,\"L-II.\":10.2},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG13577*\",\"M-I.\":2.92,\"M-II.\":7.7,\"Cs-I.\":1.63,\"Cs-II.\":5.6,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00541*\",\"M-I.\":2.8,\"M-II.\":2.68,\"Cs-I.\":3.27,\"Cs-II.\":3.5,\"L-I.\":3.5,\"L-II.\":9.71},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00530\",\"M-I.\":3.62,\"M-II.\":6.07,\"Cs-I.\":1.87,\"Cs-II.\":2.92,\"L-I.\":3.33,\"L-II.\":7.7},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00544\",\"M-I.\":10.4,\"M-II.\":10.9,\"Cs-I.\":2.22,\"Cs-II.\":1.98,\"L-I.\":2.19,\"L-II.\":3.41},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00539\",\"M-I.\":0.0,\"M-II.\":0.0,\"Cs-I.\":0.0,\"Cs-II.\":0.0,\"L-I.\":3.41,\"L-II.\":20.7},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00339\",\"M-I.\":4.55,\"M-II.\":5.25,\"Cs-I.\":1.87,\"Cs-II.\":6.07,\"L-I.\":1.49,\"L-II.\":2.36},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N4510\",\"M-I.\":3.15,\"M-II.\":2.57,\"Cs-I.\":1.28,\"Cs-II.\":2.33,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00540*\",\"M-I.\":2.57,\"M-II.\":3.97,\"Cs-I.\":0.0,\"Cs-II.\":0.0,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"WALLER\",\"M-I.\":0.7,\"M-II.\":3.27,\"Cs-I.\":1.28,\"Cs-II.\":1.98,\"L-I.\":3.24,\"L-II.\":10.2},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2123*\",\"M-I.\":2.33,\"M-II.\":2.8,\"Cs-I.\":2.57,\"Cs-II.\":2.8,\"L-I.\":1.93,\"L-II.\":3.68},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2239\",\"M-I.\":3.27,\"M-II.\":8.75,\"Cs-I.\":3.27,\"Cs-II.\":2.33,\"L-I.\":2.45,\"L-II.\":16.8},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2249\",\"M-I.\":2.68,\"M-II.\":1.4,\"Cs-I.\":3.27,\"Cs-II.\":2.68,\"L-I.\":3.68,\"L-II.\":11.6},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H1881\",\"M-I.\":3.38,\"M-II.\":9.45,\"Cs-I.\":2.22,\"Cs-II.\":1.75,\"L-I.\":4.73,\"L-II.\":12.7},{\"diagramhoz\":\"Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2127\",\"M-I.\":4.9,\"M-II.\":7.82,\"Cs-I.\":1.63,\"Cs-II.\":3.85,\"L-I.\":2.1,\"L-II.\":7.44},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG11227*\",\"M-I.\":58.8,\"M-II.\":108.0,\"Cs-I.\":89.7,\"Cs-II.\":103.0,\"L-I.\":110.0,\"L-II.\":176.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG8492\",\"M-I.\":62.2,\"M-II.\":70.6,\"Cs-I.\":96.3,\"Cs-II.\":103.0,\"L-I.\":158.0,\"L-II.\":173.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG17219\",\"M-I.\":66.6,\"M-II.\":103.0,\"Cs-I.\":94.1,\"Cs-II.\":128.0,\"L-I.\":136.0,\"L-II.\":138.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG1578\",\"M-I.\":61.7,\"M-II.\":118.0,\"Cs-I.\":113.0,\"Cs-II.\":142.0,\"L-I.\":145.0,\"L-II.\":173.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"UG13577*\",\"M-I.\":66.4,\"M-II.\":65.5,\"Cs-I.\":102.0,\"Cs-II.\":123.0,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00541*\",\"M-I.\":44.1,\"M-II.\":88.1,\"Cs-I.\":62.9,\"Cs-II.\":116.0,\"L-I.\":155.0,\"L-II.\":186.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00530\",\"M-I.\":36.4,\"M-II.\":65.5,\"Cs-I.\":95.5,\"Cs-II.\":103.0,\"L-I.\":171.0,\"L-II.\":141.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00544\",\"M-I.\":76.4,\"M-II.\":102.0,\"Cs-I.\":111.0,\"Cs-II.\":155.0,\"L-I.\":102.0,\"L-II.\":164.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00539\",\"M-I.\":0.0,\"M-II.\":0.0,\"Cs-I.\":0.0,\"Cs-II.\":0.0,\"L-I.\":149.0,\"L-II.\":165.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00339\",\"M-I.\":52.9,\"M-II.\":98.5,\"Cs-I.\":94.4,\"Cs-II.\":148.0,\"L-I.\":133.0,\"L-II.\":162.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N4510\",\"M-I.\":44.9,\"M-II.\":81.7,\"Cs-I.\":57.8,\"Cs-II.\":111.0,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"N00540*\",\"M-I.\":52.5,\"M-II.\":63.8,\"Cs-I.\":0.0,\"Cs-II.\":0.0,\"L-I.\":0.0,\"L-II.\":0.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"WALLER\",\"M-I.\":41.7,\"M-II.\":76.7,\"Cs-I.\":109.0,\"Cs-II.\":121.0,\"L-I.\":145.0,\"L-II.\":129.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2123*\",\"M-I.\":52.7,\"M-II.\":64.2,\"Cs-I.\":77.5,\"Cs-II.\":128.0,\"L-I.\":120.0,\"L-II.\":164.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2239\",\"M-I.\":80.2,\"M-II.\":104.0,\"Cs-I.\":108.0,\"Cs-II.\":142.0,\"L-I.\":148.0,\"L-II.\":157.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2249\",\"M-I.\":31.1,\"M-II.\":88.8,\"Cs-I.\":59.8,\"Cs-II.\":106.0,\"L-I.\":111.0,\"L-II.\":153.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H1881\",\"M-I.\":64.6,\"M-II.\":116.0,\"Cs-I.\":111.0,\"Cs-II.\":130.0,\"L-I.\":135.0,\"L-II.\":135.0},{\"diagramhoz\":\"Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha\",\"fajta\":\"H2127\",\"M-I.\":73.9,\"M-II.\":115.0,\"Cs-I.\":104.0,\"Cs-II.\":149.0,\"L-I.\":142.0,\"L-II.\":172.0}]}"));}),
"[project]/src/utils/dataProcessor.ts [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "BREEDERS",
    ()=>BREEDERS,
    "LOCATION_GROUPS",
    ()=>LOCATION_GROUPS,
    "createChartSeriesData",
    ()=>createChartSeriesData,
    "getBreederColor",
    ()=>getBreederColor,
    "getBreederForVariety",
    ()=>getBreederForVariety,
    "groupDataByBreeder",
    ()=>groupDataByBreeder,
    "processChartData",
    ()=>processChartData
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$raw_excel_data$2e$json__$28$json$29$__ = __turbopack_context__.i("[project]/src/data/raw_excel_data.json (json)");
;
const BREEDERS = [
    {
        name: 'Unigen Seeds',
        color: '#dc2626',
        varieties: [
            'UG11227*',
            'UG8492',
            'UG17219',
            'UG1578',
            'UG13577*'
        ]
    },
    {
        name: 'BASF-Nunhems',
        color: '#d97706',
        varieties: [
            'N00541*',
            'N00530',
            'N00544',
            'N00539',
            'N00339',
            'N4510',
            'N00540*'
        ]
    },
    {
        name: 'Waller + Heinz',
        color: '#1e40af',
        varieties: [
            'WALLER',
            'H2123*',
            'H2239',
            'H2249',
            'H1881',
            'H2127'
        ]
    }
];
const LOCATION_GROUPS = [
    {
        name: 'Mezőberény',
        locations: [
            'M-I',
            'M-II'
        ],
        color: '#8b5cf6'
    },
    {
        name: 'Csabacsűd',
        locations: [
            'Cs-I',
            'Cs-II'
        ],
        color: '#06b6d4'
    },
    {
        name: 'Lakitelek',
        locations: [
            'L-I',
            'L-II'
        ],
        color: '#84cc16'
    }
];
function getBreederForVariety(variety) {
    for (const breeder of BREEDERS){
        if (breeder.varieties.includes(variety)) {
            return breeder.name;
        }
    }
    return 'Ismeretlen';
}
function getBreederColor(breederName) {
    const breeder = BREEDERS.find((b)=>b.name === breederName);
    return breeder?.color || '#6b7280';
}
function processChartData(chartType) {
    const targetDiagram = chartType === 'érett' ? 'Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha' : 'Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha';
    const filteredData = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$raw_excel_data$2e$json__$28$json$29$__["default"].Munka1.filter((item)=>item.diagramhoz === targetDiagram);
    return filteredData.map((item)=>({
            variety: item.fajta,
            breeder: getBreederForVariety(item.fajta),
            locations: {
                'M-I': item['M-I.'],
                'M-II': item['M-II.'],
                'Cs-I': item['Cs-I.'],
                'Cs-II': item['Cs-II.'],
                'L-I': item['L-I.'],
                'L-II': item['L-II.']
            }
        }));
}
function groupDataByBreeder(data) {
    return data.reduce((acc, item)=>{
        if (!acc[item.breeder]) {
            acc[item.breeder] = [];
        }
        acc[item.breeder].push(item);
        return acc;
    }, {});
}
function createChartSeriesData(varieties, breederColor) {
    const locations = [
        'M-I',
        'M-II',
        'Cs-I',
        'Cs-II',
        'L-I',
        'L-II'
    ];
    return varieties.map((variety, varietyIndex)=>({
            name: variety.variety,
            data: locations.map((location, locationIndex)=>({
                    name: location,
                    y: variety.locations[location],
                    color: adjustColorBrightness(breederColor, varietyIndex * 0.2)
                })),
            color: adjustColorBrightness(breederColor, varietyIndex * 0.2)
        }));
}
function adjustColorBrightness(hex, factor) {
    // Egyszerű színárnyalat módosítás
    const num = parseInt(hex.replace('#', ''), 16);
    const amt = Math.round(2.55 * factor * 100);
    const R = (num >> 16) + amt;
    const G = (num >> 8 & 0x00FF) + amt;
    const B = (num & 0x0000FF) + amt;
    return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 + (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 + (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}
}),
"[project]/src/app/page.tsx [app-ssr] (ecmascript)", ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s([
    "default",
    ()=>Home
]);
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$BreederChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/BreederChart.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/dataProcessor.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
function Home() {
    // Adatok feldolgozása
    const erettData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processChartData"])('érett');
    const romloData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["processChartData"])('romló');
    const erettGrouped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["groupDataByBreeder"])(erettData);
    const romloGrouped = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["groupDataByBreeder"])(romloData);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "min-h-screen p-6",
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-[1920px] mx-auto space-y-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center space-y-2",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground",
                            children: "🍅 Univer 2025 Dashboard"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 19,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-base sm:text-lg text-muted-foreground",
                            children: "Tövön tarthatóság elemzés nemesítőházak szerint"
                        }, void 0, false, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 22,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 18,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "grid grid-cols-1 lg:grid-cols-2 gap-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-2xl sm:text-3xl font-bold mb-2 text-foreground",
                                            children: "Érett bogyó mennyisége (t/ha)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 32,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm sm:text-base text-muted-foreground",
                                            children: "Az ép, érett bogyó mennyisége I. és II. szedés során"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 35,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 31,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BREEDERS"].map((breeder)=>{
                                        const varieties = erettGrouped[breeder.name] || [];
                                        if (varieties.length === 0) return null;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full bg-card border border-border rounded-lg p-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mb-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg sm:text-xl font-semibold flex items-center gap-3 text-foreground",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "w-4 h-4 rounded-full",
                                                                    style: {
                                                                        backgroundColor: breeder.color
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 49,
                                                                    columnNumber: 25
                                                                }, this),
                                                                breeder.name
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 48,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-muted-foreground",
                                                            children: [
                                                                varieties.length,
                                                                " fajta adatai"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 55,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 47,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$BreederChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    title: "Érett bogyó mennyisége",
                                                    varieties: varieties,
                                                    breederColor: breeder.color,
                                                    breederName: breeder.name,
                                                    allVarietiesData: erettData
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 59,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `erett-${breeder.name}`, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 46,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 40,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 30,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "space-y-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                            className: "text-2xl sm:text-3xl font-bold mb-2 text-foreground",
                                            children: "Romló bogyó mennyisége (t/ha)"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 75,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm sm:text-base text-muted-foreground",
                                            children: "A romló bogyó mennyisége I. és II. szedés során"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 78,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 74,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-6",
                                    children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$dataProcessor$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["BREEDERS"].map((breeder)=>{
                                        const varieties = romloGrouped[breeder.name] || [];
                                        if (varieties.length === 0) return null;
                                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full bg-card border border-border rounded-lg p-6",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "mb-4",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                            className: "text-lg sm:text-xl font-semibold flex items-center gap-3 text-foreground",
                                                            children: [
                                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                                    className: "w-4 h-4 rounded-full",
                                                                    style: {
                                                                        backgroundColor: breeder.color
                                                                    }
                                                                }, void 0, false, {
                                                                    fileName: "[project]/src/app/page.tsx",
                                                                    lineNumber: 92,
                                                                    columnNumber: 25
                                                                }, this),
                                                                breeder.name
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 91,
                                                            columnNumber: 23
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                            className: "text-sm text-muted-foreground",
                                                            children: [
                                                                varieties.length,
                                                                " fajta adatai"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/page.tsx",
                                                            lineNumber: 98,
                                                            columnNumber: 23
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 90,
                                                    columnNumber: 21
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$BreederChart$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {
                                                    title: "Romló bogyó mennyisége",
                                                    varieties: varieties,
                                                    breederColor: breeder.color,
                                                    breederName: breeder.name,
                                                    allVarietiesData: romloData
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/page.tsx",
                                                    lineNumber: 102,
                                                    columnNumber: 21
                                                }, this)
                                            ]
                                        }, `romlo-${breeder.name}`, true, {
                                            fileName: "[project]/src/app/page.tsx",
                                            lineNumber: 89,
                                            columnNumber: 19
                                        }, this);
                                    })
                                }, void 0, false, {
                                    fileName: "[project]/src/app/page.tsx",
                                    lineNumber: 83,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/page.tsx",
                            lineNumber: 73,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 28,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mt-12 pt-8 border-t border-border text-center",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-muted-foreground",
                        children: "🍅 Paradicsom fajtakísérlet - 2025 © Minden jog fenntartva"
                    }, void 0, false, {
                        fileName: "[project]/src/app/page.tsx",
                        lineNumber: 118,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/page.tsx",
                    lineNumber: 117,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/page.tsx",
            lineNumber: 16,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/page.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
}),
];

//# sourceMappingURL=src_ebda11b4._.js.map