{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/BreederChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useMemo, useId } from 'react';\r\nimport Highcharts from 'highcharts';\r\nimport HighchartsReact from 'highcharts-react-official';\r\nimport { ProcessedData } from '@/utils/dataProcessor';\r\nimport { useTheme } from './ThemeProvider';\r\nimport { useChartPanel } from '@/contexts/ChartPanelContext';\r\n\r\ninterface BreederChartProps {\r\n  title: string;\r\n  varieties: ProcessedData[];\r\n  breederColor: string;\r\n  breederName: string;\r\n  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez\r\n}\r\n\r\n// Információs panel komponens a BreederChart-hoz\r\nconst BreederDataInfoPanel: React.FC<{\r\n  selectedData: any | null;\r\n  varieties: ProcessedData[];\r\n  theme: string;\r\n  onClose: () => void;\r\n}> = ({ selectedData, varieties, theme, onClose }) => {\r\n  if (!selectedData) {\r\n    return null; // Ne jelenjen meg semmi, ha nincs kiv<PERSON>lasztott adat\r\n  }\r\n\r\n  // Statisztikák számítása (0 értékeket kihagyva)\r\n  const nonZeroValues = selectedData.allLocationData.filter((d: any) => d.value > 0).map((d: any) => d.value);\r\n  const avgValue = nonZeroValues.length > 0 ? nonZeroValues.reduce((sum: number, val: number) => sum + val, 0) / nonZeroValues.length : 0;\r\n\r\n  // Helyszín nevek mapping\r\n  const locationNames: { [key: string]: string } = {\r\n    'M-I': 'Mezőberény I.',\r\n    'M-II': 'Mezőberény II.',\r\n    'Cs-I': 'Csabacsűd I.',\r\n    'Cs-II': 'Csabacsűd II.',\r\n    'L-I': 'Lakitelek I.',\r\n    'L-II': 'Lakitelek II.'\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full max-w-sm bg-card/95 backdrop-blur-sm border border-border rounded-lg p-4 transition-all duration-300 shadow-lg\">\r\n      {/* Fejléc - kompakt + bezárás gomb */}\r\n      <div className=\"flex items-center justify-between mb-3\">\r\n        <div className=\"flex items-center flex-1 min-w-0\">\r\n          <div\r\n            className=\"w-3 h-3 rounded-full mr-2 flex-shrink-0\"\r\n            style={{ backgroundColor: selectedData.seriesColor }}\r\n          ></div>\r\n          <h3 className=\"text-sm font-semibold text-foreground truncate\">{selectedData.variety}</h3>\r\n        </div>\r\n        <button\r\n          onClick={onClose}\r\n          className=\"ml-2 p-1 hover:bg-muted rounded-full transition-colors duration-200 flex-shrink-0\"\r\n          title=\"Bezárás\"\r\n        >\r\n          <svg className=\"w-4 h-4 text-muted-foreground hover:text-foreground\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\r\n          </svg>\r\n        </button>\r\n      </div>\r\n\r\n      {/* Helyszín adatok - kompakt lista */}\r\n      <div className=\"space-y-1 mb-3\">\r\n        {selectedData.allLocationData.map((data: any, index: number) => {\r\n          if (data.value === 0) return null; // 0 értékeket kihagyjuk\r\n\r\n          const isCurrentPoint = data.location === selectedData.location;\r\n          return (\r\n            <div\r\n              key={index}\r\n              className={`flex justify-between items-center py-1 px-2 rounded text-xs ${\r\n                isCurrentPoint\r\n                  ? 'bg-primary/20 border border-primary/30 font-medium'\r\n                  : 'bg-muted/30'\r\n              }`}\r\n            >\r\n              <span className={isCurrentPoint ? 'text-foreground' : 'text-muted-foreground'}>\r\n                {locationNames[data.location] || data.location}\r\n              </span>\r\n              <span className={isCurrentPoint ? 'text-foreground font-semibold' : 'text-foreground'}>\r\n                {data.value.toFixed(1)}\r\n              </span>\r\n            </div>\r\n          );\r\n        })}\r\n      </div>\r\n\r\n      {/* Átlag - csak ha van nem-nulla érték */}\r\n      {avgValue > 0 && (\r\n        <div className=\"border-t border-border pt-2\">\r\n          <div className=\"flex justify-between items-center text-xs\">\r\n            <span className=\"text-muted-foreground\">Átlag:</span>\r\n            <span className=\"font-semibold text-foreground\">{avgValue.toFixed(1)}</span>\r\n          </div>\r\n        </div>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nconst BreederChart: React.FC<BreederChartProps> = ({\r\n  title,\r\n  varieties,\r\n  breederColor,\r\n  breederName,\r\n  allVarietiesData = []\r\n}) => {\r\n  const chartRef = React.useRef<HTMLDivElement>(null);\r\n  const { theme } = useTheme();\r\n  const { setActiveChart, closePanel, isChartActive, selectedData } = useChartPanel();\r\n\r\n  // Egyedi azonosító generálása minden chart példányhoz\r\n  const chartId = useId();\r\n\r\n  // Modulok betöltése komponens betöltéskor (egyszerűsített)\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Egyszerűen beállítjuk a Highcharts alapbeállításokat\r\n      // Az export funkciók automatikusan működnek\r\n      console.log('Highcharts initialized with export support');\r\n    }\r\n  }, []);\r\n\r\n  // Dinamikus színek a téma alapján - külön színek sötét és világos módhoz\r\n  const themeColors = useMemo(() => {\r\n    if (theme === 'dark') {\r\n      // SÖTÉT MÓD - Világos színek sötét háttéren\r\n      return {\r\n        background: 'transparent',\r\n        titleColor: '#f8fafc',           // Tiszta fehér címek\r\n        subtitleColor: '#cbd5e1',        // Világos szürke alcímek\r\n        labelColor: '#94a3b8',           // Közepes világos szürke labelek\r\n        gridLineColor: '#475569',        // Sötét szürke vonalak\r\n        lineColor: '#475569',            // Sötét szürke tengelyek\r\n        crosshairColor: 'rgba(248, 250, 252, 0.4)', // Világos crosshair\r\n        plotBandColor: 'rgba(248, 250, 252, 0.08)',\r\n        plotBandColorAlt: 'rgba(248, 250, 252, 0.15)',\r\n        tooltipBg: 'rgba(15, 23, 42, 0.95)',        // Sötét tooltip háttér\r\n        tooltipBorder: '#475569',                    // Sötét keret\r\n        tooltipText: '#f8fafc',                      // Világos tooltip szöveg\r\n        exportButtonBg: 'rgba(51, 65, 85, 0.9)',\r\n        exportButtonHover: 'rgba(71, 85, 105, 0.95)',\r\n        exportButtonStroke: '#f8fafc'\r\n      };\r\n    } else {\r\n      // VILÁGOS MÓD - Sötét színek világos háttéren\r\n      return {\r\n        background: 'transparent',\r\n        titleColor: '#0f172a',           // Mély sötét címek\r\n        subtitleColor: '#334155',        // Sötét szürke alcímek\r\n        labelColor: '#64748b',           // Közepes sötét szürke labelek\r\n        gridLineColor: '#e2e8f0',        // Világos szürke vonalak\r\n        lineColor: '#e2e8f0',            // Világos szürke tengelyek\r\n        crosshairColor: 'rgba(15, 23, 42, 0.4)',    // Sötét crosshair\r\n        plotBandColor: 'rgba(15, 23, 42, 0.06)',\r\n        plotBandColorAlt: 'rgba(15, 23, 42, 0.12)',\r\n        tooltipBg: 'rgba(255, 255, 255, 0.95)',     // Világos tooltip háttér\r\n        tooltipBorder: '#e2e8f0',                    // Világos keret\r\n        tooltipText: '#0f172a',                      // Sötét tooltip szöveg\r\n        exportButtonBg: 'rgba(248, 250, 252, 0.9)',\r\n        exportButtonHover: 'rgba(226, 232, 240, 0.95)',\r\n        exportButtonStroke: '#0f172a'\r\n      };\r\n    }\r\n  }, [theme]);\r\n  // Színárnyalatok generálása a fajtákhoz\r\n  const generateColorShades = (baseColor: string, count: number): string[] => {\r\n    const colors: string[] = [];\r\n    for (let i = 0; i < count; i++) {\r\n      // Ha WALLER fajtáról van szó, akkor zöld színt használunk\r\n      if (varieties[i]?.variety === 'WALLER') {\r\n        colors.push('#16a34a'); // Zöld szín a WALLER fajtának\r\n      } else {\r\n        const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);\r\n        colors.push(adjustColorBrightness(baseColor, factor));\r\n      }\r\n    }\r\n    return colors;\r\n  };\r\n\r\n  const adjustColorBrightness = (hex: string, factor: number): string => {\r\n    const num = parseInt(hex.replace('#', ''), 16);\r\n    const R = Math.round((num >> 16) * factor);\r\n    const G = Math.round(((num >> 8) & 0x00FF) * factor);\r\n    const B = Math.round((num & 0x0000FF) * factor);\r\n    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');\r\n  };\r\n\r\n  const colors = generateColorShades(breederColor, varieties.length);\r\n\r\n  // Adatok előkészítése Highcharts számára\r\n  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\r\n  \r\n  const series = varieties.map((variety, index) => ({\r\n    type: 'column' as const,\r\n    name: variety.variety,\r\n    data: categories.map(location =>\r\n      variety.locations[location as keyof typeof variety.locations]\r\n    ),\r\n    color: colors[index]\r\n  }));\r\n\r\n  const options: Highcharts.Options = {\r\n    chart: {\r\n      type: 'column',\r\n      backgroundColor: themeColors.background,\r\n      style: {\r\n        fontFamily: 'var(--font-geist-sans)'\r\n      },\r\n      animation: false\r\n    },\r\n    title: {\r\n      text: `${breederName}`,\r\n      style: {\r\n        color: themeColors.titleColor,\r\n        fontSize: '18px',\r\n        fontWeight: '600'\r\n      }\r\n    },\r\n    subtitle: {\r\n      text: title,\r\n      style: {\r\n        color: themeColors.subtitleColor,\r\n        fontSize: '14px'\r\n      }\r\n    },\r\n    xAxis: {\r\n      categories: categories,\r\n      labels: {\r\n        style: {\r\n          color: themeColors.labelColor\r\n        }\r\n      },\r\n      lineColor: themeColors.lineColor,\r\n      tickColor: themeColors.lineColor,\r\n      crosshair: {\r\n        width: 1,\r\n        color: themeColors.crosshairColor,\r\n        dashStyle: 'Solid' as const\r\n      },\r\n      plotBands: [\r\n        {\r\n          from: -0.5,\r\n          to: 1.5,\r\n          color: themeColors.plotBandColor,\r\n          label: {\r\n            text: 'Mezőberény',\r\n            style: {\r\n              color: themeColors.labelColor,\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 1.5,\r\n          to: 3.5,\r\n          color: themeColors.plotBandColorAlt,\r\n          label: {\r\n            text: 'Csabacsűd',\r\n            style: {\r\n              color: themeColors.labelColor,\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 3.5,\r\n          to: 5.5,\r\n          color: themeColors.plotBandColor,\r\n          label: {\r\n            text: 'Lakitelek',\r\n            style: {\r\n              color: themeColors.labelColor,\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    yAxis: {\r\n      title: {\r\n        text: 't/ha',\r\n        style: {\r\n          color: themeColors.labelColor\r\n        }\r\n      },\r\n      labels: {\r\n        style: {\r\n          color: themeColors.labelColor\r\n        }\r\n      },\r\n      gridLineColor: themeColors.gridLineColor\r\n    },\r\n    legend: {\r\n      enabled: true,\r\n      itemStyle: {\r\n        color: themeColors.labelColor\r\n      },\r\n      itemHoverStyle: {\r\n        color: themeColors.titleColor\r\n      }\r\n    },\r\n    plotOptions: {\r\n      column: {\r\n        animation: false,\r\n        borderWidth: 0,\r\n        borderRadius: 3,\r\n        groupPadding: 0.1,\r\n        pointPadding: 0.05,\r\n        dataLabels: {\r\n          enabled: false\r\n        },\r\n        states: {\r\n          hover: {\r\n            brightness: 0.2,\r\n            borderColor: themeColors.titleColor,\r\n            borderWidth: 2\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        },\r\n        cursor: 'pointer',\r\n        point: {\r\n          events: {\r\n            click: function(this: Highcharts.Point) {\r\n              const point = this as any;\r\n              const series = point.series;\r\n              const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\r\n\r\n              // Összegyűjtjük az adott fajta összes helyszínének adatait\r\n              const allLocationData = categories.map(location => {\r\n                const locationIndex = categories.indexOf(location);\r\n                const value = series.data[locationIndex] ? series.data[locationIndex].y : 0;\r\n                return {\r\n                  location,\r\n                  value\r\n                };\r\n              });\r\n\r\n              // Globális state frissítése\r\n              setActiveChart(chartId, {\r\n                variety: series.name,\r\n                location: point.category,\r\n                value: point.y,\r\n                seriesColor: series.color,\r\n                allLocationData\r\n              });\r\n            },\r\n            mouseOver: function() {\r\n              const chart = this.series.chart;\r\n              const point = this;\r\n              const varietyName = point.series.name;\r\n\r\n              // Kiemeljük az összes ugyanolyan fajta oszlopot\r\n              chart.series.forEach((series: any) => {\r\n                if (series.name === varietyName) {\r\n                  // Kiemeljük az aktív fajta oszlopait\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      color: Highcharts.color(series.color).brighten(0.2).get(),\r\n                      borderColor: '#ffffff',\r\n                      borderWidth: 2\r\n                    }, false);\r\n                  });\r\n                  series.update({\r\n                    opacity: 1\r\n                  }, false);\r\n                } else {\r\n                  // Elhalványítjuk a többi fajtát\r\n                  series.update({\r\n                    opacity: 0.3\r\n                  }, false);\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      opacity: 0.3\r\n                    }, false);\r\n                  });\r\n                }\r\n              });\r\n\r\n              chart.redraw();\r\n            },\r\n            mouseOut: function() {\r\n              const chart = this.series.chart;\r\n\r\n              // Visszaállítjuk az eredeti állapotot\r\n              chart.series.forEach((series: any) => {\r\n                series.update({\r\n                  opacity: 1\r\n                }, false);\r\n                series.points.forEach((p: any) => {\r\n                  p.update({\r\n                    color: series.color,\r\n                    borderColor: undefined,\r\n                    borderWidth: 0,\r\n                    opacity: 1\r\n                  }, false);\r\n                });\r\n              });\r\n\r\n              chart.redraw();\r\n            }\r\n          }\r\n        },\r\n        stickyTracking: true\r\n      },\r\n      series: {\r\n        states: {\r\n          hover: {\r\n            enabled: true\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        }\r\n      }\r\n    },\r\n    series: series,\r\n    credits: {\r\n      enabled: false\r\n    },\r\n    exporting: {\r\n      enabled: true,\r\n      buttons: {\r\n        contextButton: {\r\n          enabled: true,\r\n          theme: {\r\n            fill: themeColors.exportButtonBg,\r\n            stroke: themeColors.exportButtonStroke,\r\n            r: 4,\r\n            states: {\r\n              hover: {\r\n                fill: themeColors.exportButtonHover,\r\n                stroke: themeColors.exportButtonStroke\r\n              },\r\n              select: {\r\n                fill: themeColors.exportButtonHover,\r\n                stroke: themeColors.exportButtonStroke\r\n              }\r\n            }\r\n          } as any,\r\n          menuItems: [\r\n            'viewFullscreen',\r\n            'separator',\r\n            'downloadPNG',\r\n            'downloadJPEG',\r\n            'downloadSVG'\r\n          ],\r\n          x: -10,\r\n          y: 10\r\n        }\r\n      }\r\n    },\r\n    navigation: {\r\n      buttonOptions: {\r\n        enabled: true\r\n      }\r\n    },\r\n    tooltip: {\r\n      enabled: false\r\n    }\r\n  };\r\n\r\n  // Ellenőrizzük, hogy ez a chart aktív-e\r\n  const isThisChartActive = isChartActive(chartId);\r\n  const currentSelectedData = isThisChartActive ? selectedData : null;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      <div className={`flex ${currentSelectedData ? 'flex-col lg:flex-row' : ''} gap-4`}>\r\n        <div className={`${currentSelectedData ? 'flex-1' : 'w-full'} h-96 relative transition-all duration-300`}>\r\n          <button\r\n            onClick={() => {\r\n              if (chartRef.current) {\r\n                if (chartRef.current.requestFullscreen) {\r\n                  chartRef.current.requestFullscreen();\r\n                } else if ((chartRef.current as any).webkitRequestFullscreen) {\r\n                  (chartRef.current as any).webkitRequestFullscreen();\r\n                } else if ((chartRef.current as any).mozRequestFullScreen) {\r\n                  (chartRef.current as any).mozRequestFullScreen();\r\n                } else if ((chartRef.current as any).msRequestFullscreen) {\r\n                  (chartRef.current as any).msRequestFullscreen();\r\n                }\r\n              }\r\n            }}\r\n            className=\"absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg\"\r\n            title=\"Teljes képernyő\"\r\n          >\r\n            <svg\r\n              className=\"w-5 h-5\"\r\n              fill=\"none\"\r\n              stroke=\"currentColor\"\r\n              viewBox=\"0 0 24 24\"\r\n              xmlns=\"http://www.w3.org/2000/svg\"\r\n            >\r\n              <path\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                strokeWidth={2}\r\n                d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\r\n              />\r\n            </svg>\r\n          </button>\r\n          <div ref={chartRef} className=\"w-full h-96\">\r\n            <HighchartsReact\r\n              highcharts={Highcharts}\r\n              options={options}\r\n            />\r\n          </div>\r\n        </div>\r\n        {currentSelectedData && (\r\n          <div className=\"lg:flex-shrink-0\">\r\n            <BreederDataInfoPanel\r\n              selectedData={currentSelectedData}\r\n              varieties={varieties}\r\n              theme={theme}\r\n              onClose={closePanel}\r\n            />\r\n          </div>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BreederChart;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAEA;AACA;AAPA;;;;;;;AAiBA,iDAAiD;AACjD,MAAM,uBAKD,CAAC,EAAE,YAAY,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE;IAC/C,IAAI,CAAC,cAAc;QACjB,OAAO,MAAM,oDAAoD;IACnE;IAEA,gDAAgD;IAChD,MAAM,gBAAgB,aAAa,eAAe,CAAC,MAAM,CAAC,CAAC,IAAW,EAAE,KAAK,GAAG,GAAG,GAAG,CAAC,CAAC,IAAW,EAAE,KAAK;IAC1G,MAAM,WAAW,cAAc,MAAM,GAAG,IAAI,cAAc,MAAM,CAAC,CAAC,KAAa,MAAgB,MAAM,KAAK,KAAK,cAAc,MAAM,GAAG;IAEtI,yBAAyB;IACzB,MAAM,gBAA2C;QAC/C,OAAO;QACP,QAAQ;QACR,QAAQ;QACR,SAAS;QACT,OAAO;QACP,QAAQ;IACV;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,iBAAiB,aAAa,WAAW;gCAAC;;;;;;0CAErD,8OAAC;gCAAG,WAAU;0CAAkD,aAAa,OAAO;;;;;;;;;;;;kCAEtF,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC;4BAAI,WAAU;4BAAsD,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC7G,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM3E,8OAAC;gBAAI,WAAU;0BACZ,aAAa,eAAe,CAAC,GAAG,CAAC,CAAC,MAAW;oBAC5C,IAAI,KAAK,KAAK,KAAK,GAAG,OAAO,MAAM,wBAAwB;oBAE3D,MAAM,iBAAiB,KAAK,QAAQ,KAAK,aAAa,QAAQ;oBAC9D,qBACE,8OAAC;wBAEC,WAAW,CAAC,4DAA4D,EACtE,iBACI,uDACA,eACJ;;0CAEF,8OAAC;gCAAK,WAAW,iBAAiB,oBAAoB;0CACnD,aAAa,CAAC,KAAK,QAAQ,CAAC,IAAI,KAAK,QAAQ;;;;;;0CAEhD,8OAAC;gCAAK,WAAW,iBAAiB,kCAAkC;0CACjE,KAAK,KAAK,CAAC,OAAO,CAAC;;;;;;;uBAXjB;;;;;gBAeX;;;;;;YAID,WAAW,mBACV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAwB;;;;;;sCACxC,8OAAC;4BAAK,WAAU;sCAAiC,SAAS,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;AAM9E;AAEA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,SAAS,EACT,YAAY,EACZ,WAAW,EACX,mBAAmB,EAAE,EACtB;IACC,MAAM,WAAW,gNAAK,CAAC,MAAM,CAAiB;IAC9C,MAAM,EAAE,KAAK,EAAE,GAAG,IAAA,+IAAQ;IAC1B,MAAM,EAAE,cAAc,EAAE,UAAU,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,IAAA,sJAAa;IAEjF,sDAAsD;IACtD,MAAM,UAAU,IAAA,8MAAK;IAErB,2DAA2D;IAC3D,IAAA,kNAAS,EAAC;QACR;;IAKF,GAAG,EAAE;IAEL,yEAAyE;IACzE,MAAM,cAAc,IAAA,gNAAO,EAAC;QAC1B,IAAI,UAAU,QAAQ;YACpB,4CAA4C;YAC5C,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,eAAe;gBACf,WAAW;gBACX,gBAAgB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,WAAW;gBACX,eAAe;gBACf,aAAa;gBACb,gBAAgB;gBAChB,mBAAmB;gBACnB,oBAAoB;YACtB;QACF,OAAO;YACL,8CAA8C;YAC9C,OAAO;gBACL,YAAY;gBACZ,YAAY;gBACZ,eAAe;gBACf,YAAY;gBACZ,eAAe;gBACf,WAAW;gBACX,gBAAgB;gBAChB,eAAe;gBACf,kBAAkB;gBAClB,WAAW;gBACX,eAAe;gBACf,aAAa;gBACb,gBAAgB;gBAChB,mBAAmB;gBACnB,oBAAoB;YACtB;QACF;IACF,GAAG;QAAC;KAAM;IACV,wCAAwC;IACxC,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,0DAA0D;YAC1D,IAAI,SAAS,CAAC,EAAE,EAAE,YAAY,UAAU;gBACtC,OAAO,IAAI,CAAC,YAAY,8BAA8B;YACxD,OAAO;gBACL,MAAM,SAAS,MAAM,AAAC,IAAI,MAAO,KAAK,GAAG,CAAC,QAAQ,GAAG;gBACrD,OAAO,IAAI,CAAC,sBAAsB,WAAW;YAC/C;QACF;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI;QACnC,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,AAAC,OAAO,IAAK,MAAM,IAAI;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,QAAQ,IAAI;QACxC,OAAO,MAAM,CAAC,AAAC,KAAK,KAAO,KAAK,IAAK,CAAC,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;IACnE;IAEA,MAAM,SAAS,oBAAoB,cAAc,UAAU,MAAM;IAEjE,yCAAyC;IACzC,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAElE,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;YAChD,MAAM;YACN,MAAM,QAAQ,OAAO;YACrB,MAAM,WAAW,GAAG,CAAC,CAAA,WACnB,QAAQ,SAAS,CAAC,SAA2C;YAE/D,OAAO,MAAM,CAAC,MAAM;QACtB,CAAC;IAED,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB,YAAY,UAAU;YACvC,OAAO;gBACL,YAAY;YACd;YACA,WAAW;QACb;QACA,OAAO;YACL,MAAM,GAAG,aAAa;YACtB,OAAO;gBACL,OAAO,YAAY,UAAU;gBAC7B,UAAU;gBACV,YAAY;YACd;QACF;QACA,UAAU;YACR,MAAM;YACN,OAAO;gBACL,OAAO,YAAY,aAAa;gBAChC,UAAU;YACZ;QACF;QACA,OAAO;YACL,YAAY;YACZ,QAAQ;gBACN,OAAO;oBACL,OAAO,YAAY,UAAU;gBAC/B;YACF;YACA,WAAW,YAAY,SAAS;YAChC,WAAW,YAAY,SAAS;YAChC,WAAW;gBACT,OAAO;gBACP,OAAO,YAAY,cAAc;gBACjC,WAAW;YACb;YACA,WAAW;gBACT;oBACE,MAAM,CAAC;oBACP,IAAI;oBACJ,OAAO,YAAY,aAAa;oBAChC,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO,YAAY,UAAU;4BAC7B,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO,YAAY,gBAAgB;oBACnC,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO,YAAY,UAAU;4BAC7B,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO,YAAY,aAAa;oBAChC,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO,YAAY,UAAU;4BAC7B,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;aACD;QACH;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO,YAAY,UAAU;gBAC/B;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO,YAAY,UAAU;gBAC/B;YACF;YACA,eAAe,YAAY,aAAa;QAC1C;QACA,QAAQ;YACN,SAAS;YACT,WAAW;gBACT,OAAO,YAAY,UAAU;YAC/B;YACA,gBAAgB;gBACd,OAAO,YAAY,UAAU;YAC/B;QACF;QACA,aAAa;YACX,QAAQ;gBACN,WAAW;gBACX,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,YAAY;oBACV,SAAS;gBACX;gBACA,QAAQ;oBACN,OAAO;wBACL,YAAY;wBACZ,aAAa,YAAY,UAAU;wBACnC,aAAa;oBACf;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;gBACA,QAAQ;gBACR,OAAO;oBACL,QAAQ;wBACN,OAAO;4BACL,MAAM,QAAQ,IAAI;4BAClB,MAAM,SAAS,MAAM,MAAM;4BAC3B,MAAM,aAAa;gCAAC;gCAAO;gCAAQ;gCAAQ;gCAAS;gCAAO;6BAAO;4BAElE,2DAA2D;4BAC3D,MAAM,kBAAkB,WAAW,GAAG,CAAC,CAAA;gCACrC,MAAM,gBAAgB,WAAW,OAAO,CAAC;gCACzC,MAAM,QAAQ,OAAO,IAAI,CAAC,cAAc,GAAG,OAAO,IAAI,CAAC,cAAc,CAAC,CAAC,GAAG;gCAC1E,OAAO;oCACL;oCACA;gCACF;4BACF;4BAEA,4BAA4B;4BAC5B,eAAe,SAAS;gCACtB,SAAS,OAAO,IAAI;gCACpB,UAAU,MAAM,QAAQ;gCACxB,OAAO,MAAM,CAAC;gCACd,aAAa,OAAO,KAAK;gCACzB;4BACF;wBACF;wBACA,WAAW;4BACT,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAC/B,MAAM,QAAQ,IAAI;4BAClB,MAAM,cAAc,MAAM,MAAM,CAAC,IAAI;4BAErC,gDAAgD;4BAChD,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,IAAI,OAAO,IAAI,KAAK,aAAa;oCAC/B,qCAAqC;oCACrC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,OAAO,mJAAU,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG;4CACvD,aAAa;4CACb,aAAa;wCACf,GAAG;oCACL;oCACA,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;gCACL,OAAO;oCACL,gCAAgC;oCAChC,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;oCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,SAAS;wCACX,GAAG;oCACL;gCACF;4BACF;4BAEA,MAAM,MAAM;wBACd;wBACA,UAAU;4BACR,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAE/B,sCAAsC;4BACtC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,OAAO,MAAM,CAAC;oCACZ,SAAS;gCACX,GAAG;gCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;oCACrB,EAAE,MAAM,CAAC;wCACP,OAAO,OAAO,KAAK;wCACnB,aAAa;wCACb,aAAa;wCACb,SAAS;oCACX,GAAG;gCACL;4BACF;4BAEA,MAAM,MAAM;wBACd;oBACF;gBACF;gBACA,gBAAgB;YAClB;YACA,QAAQ;gBACN,QAAQ;oBACN,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;YACF;QACF;QACA,QAAQ;QACR,SAAS;YACP,SAAS;QACX;QACA,WAAW;YACT,SAAS;YACT,SAAS;gBACP,eAAe;oBACb,SAAS;oBACT,OAAO;wBACL,MAAM,YAAY,cAAc;wBAChC,QAAQ,YAAY,kBAAkB;wBACtC,GAAG;wBACH,QAAQ;4BACN,OAAO;gCACL,MAAM,YAAY,iBAAiB;gCACnC,QAAQ,YAAY,kBAAkB;4BACxC;4BACA,QAAQ;gCACN,MAAM,YAAY,iBAAiB;gCACnC,QAAQ,YAAY,kBAAkB;4BACxC;wBACF;oBACF;oBACA,WAAW;wBACT;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,GAAG,CAAC;oBACJ,GAAG;gBACL;YACF;QACF;QACA,YAAY;YACV,eAAe;gBACb,SAAS;YACX;QACF;QACA,SAAS;YACP,SAAS;QACX;IACF;IAEA,wCAAwC;IACxC,MAAM,oBAAoB,cAAc;IACxC,MAAM,sBAAsB,oBAAoB,eAAe;IAE/D,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAW,CAAC,KAAK,EAAE,sBAAsB,yBAAyB,GAAG,MAAM,CAAC;;8BAC/E,8OAAC;oBAAI,WAAW,GAAG,sBAAsB,WAAW,SAAS,0CAA0C,CAAC;;sCACtG,8OAAC;4BACC,SAAS;gCACP,IAAI,SAAS,OAAO,EAAE;oCACpB,IAAI,SAAS,OAAO,CAAC,iBAAiB,EAAE;wCACtC,SAAS,OAAO,CAAC,iBAAiB;oCACpC,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,uBAAuB,EAAE;wCAC3D,SAAS,OAAO,CAAS,uBAAuB;oCACnD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,oBAAoB,EAAE;wCACxD,SAAS,OAAO,CAAS,oBAAoB;oCAChD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,mBAAmB,EAAE;wCACvD,SAAS,OAAO,CAAS,mBAAmB;oCAC/C;gCACF;4BACF;4BACA,WAAU;4BACV,OAAM;sCAEN,cAAA,8OAAC;gCACC,WAAU;gCACV,MAAK;gCACL,QAAO;gCACP,SAAQ;gCACR,OAAM;0CAEN,cAAA,8OAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAIR,8OAAC;4BAAI,KAAK;4BAAU,WAAU;sCAC5B,cAAA,8OAAC,gMAAe;gCACd,YAAY,mJAAU;gCACtB,SAAS;;;;;;;;;;;;;;;;;gBAId,qCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBACC,cAAc;wBACd,WAAW;wBACX,OAAO;wBACP,SAAS;;;;;;;;;;;;;;;;;;;;;;AAOvB;uCAEe", "debugId": null}}, {"offset": {"line": 650, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/utils/dataProcessor.ts"], "sourcesContent": ["import rawData from '@/data/raw_excel_data.json';\n\nexport interface ChartDataPoint {\n  name: string;\n  y: number;\n  color?: string;\n}\n\nexport interface BreederGroup {\n  name: string;\n  color: string;\n  varieties: string[];\n}\n\nexport interface ProcessedData {\n  variety: string;\n  breeder: string;\n  locations: {\n    'M-I': number;\n    'M-II': number;\n    'Cs-I': number;\n    'Cs-II': number;\n    'L-I': number;\n    'L-II': number;\n  };\n}\n\n// Nemesí<PERSON><PERSON><PERSON><PERSON><PERSON> definíciója\nexport const BREEDERS: BreederGroup[] = [\n  {\n    name: 'Unigen Seeds',\n    color: '#dc2626', // Piros\n    varieties: ['UG11227*', 'UG8492', 'UG17219', 'UG1578', 'UG13577*']\n  },\n  {\n    name: 'BASF-Nunhems',\n    color: '#d97706', // Mustár narancssárga\n    varieties: ['N00541*', 'N00530', 'N00544', 'N00539', 'N00339', 'N4510', 'N00540*']\n  },\n  {\n    name: '<PERSON> + <PERSON>',\n    color: '#1e40af', // Királykék\n    varieties: ['WALLER', 'H2123*', 'H2239', 'H2249', 'H1881', 'H2127']\n  }\n];\n\n// Helyszínek csoportosítása\nexport const LOCATION_GROUPS = [\n  { name: 'Mezőberény', locations: ['M-I', 'M-II'], color: '#8b5cf6' },\n  { name: 'Csabacsűd', locations: ['Cs-I', 'Cs-II'], color: '#06b6d4' },\n  { name: 'Lakitelek', locations: ['L-I', 'L-II'], color: '#84cc16' }\n];\n\nexport function getBreederForVariety(variety: string): string {\n  for (const breeder of BREEDERS) {\n    if (breeder.varieties.includes(variety)) {\n      return breeder.name;\n    }\n  }\n  return 'Ismeretlen';\n}\n\nexport function getBreederColor(breederName: string): string {\n  const breeder = BREEDERS.find(b => b.name === breederName);\n  return breeder?.color || '#6b7280';\n}\n\nexport function processChartData(chartType: 'érett' | 'romló'): ProcessedData[] {\n  const targetDiagram = chartType === 'érett' \n    ? 'Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha'\n    : 'Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha';\n\n  const filteredData = rawData.Munka1.filter(item => item.diagramhoz === targetDiagram);\n\n  return filteredData.map(item => ({\n    variety: item.fajta,\n    breeder: getBreederForVariety(item.fajta),\n    locations: {\n      'M-I': item['M-I.'],\n      'M-II': item['M-II.'],\n      'Cs-I': item['Cs-I.'],\n      'Cs-II': item['Cs-II.'],\n      'L-I': item['L-I.'],\n      'L-II': item['L-II.']\n    }\n  }));\n}\n\nexport function groupDataByBreeder(data: ProcessedData[]): Record<string, ProcessedData[]> {\n  return data.reduce((acc, item) => {\n    if (!acc[item.breeder]) {\n      acc[item.breeder] = [];\n    }\n    acc[item.breeder].push(item);\n    return acc;\n  }, {} as Record<string, ProcessedData[]>);\n}\n\nexport function createChartSeriesData(varieties: ProcessedData[], breederColor: string): any[] {\n  const locations = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\n  \n  return varieties.map((variety, varietyIndex) => ({\n    name: variety.variety,\n    data: locations.map((location, locationIndex) => ({\n      name: location,\n      y: variety.locations[location as keyof typeof variety.locations],\n      color: adjustColorBrightness(breederColor, varietyIndex * 0.2)\n    })),\n    color: adjustColorBrightness(breederColor, varietyIndex * 0.2)\n  }));\n}\n\nfunction adjustColorBrightness(hex: string, factor: number): string {\n  // Egyszerű színárnyalat módosítás\n  const num = parseInt(hex.replace('#', ''), 16);\n  const amt = Math.round(2.55 * factor * 100);\n  const R = (num >> 16) + amt;\n  const G = (num >> 8 & 0x00FF) + amt;\n  const B = (num & 0x0000FF) + amt;\n  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +\n    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +\n    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AA4BO,MAAM,WAA2B;IACtC;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAY;YAAU;YAAW;YAAU;SAAW;IACpE;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAW;YAAU;YAAU;YAAU;YAAU;YAAS;SAAU;IACpF;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAU;YAAU;YAAS;YAAS;YAAS;SAAQ;IACrE;CACD;AAGM,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAc,WAAW;YAAC;YAAO;SAAO;QAAE,OAAO;IAAU;IACnE;QAAE,MAAM;QAAa,WAAW;YAAC;YAAQ;SAAQ;QAAE,OAAO;IAAU;IACpE;QAAE,MAAM;QAAa,WAAW;YAAC;YAAO;SAAO;QAAE,OAAO;IAAU;CACnE;AAEM,SAAS,qBAAqB,OAAe;IAClD,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,SAAS,CAAC,QAAQ,CAAC,UAAU;YACvC,OAAO,QAAQ,IAAI;QACrB;IACF;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,WAAmB;IACjD,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAC9C,OAAO,SAAS,SAAS;AAC3B;AAEO,SAAS,iBAAiB,SAA4B;IAC3D,MAAM,gBAAgB,cAAc,UAChC,8EACA;IAEJ,MAAM,eAAe,gHAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK;IAEvE,OAAO,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC/B,SAAS,KAAK,KAAK;YACnB,SAAS,qBAAqB,KAAK,KAAK;YACxC,WAAW;gBACT,OAAO,IAAI,CAAC,OAAO;gBACnB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,SAAS,IAAI,CAAC,SAAS;gBACvB,OAAO,IAAI,CAAC,OAAO;gBACnB,QAAQ,IAAI,CAAC,QAAQ;YACvB;QACF,CAAC;AACH;AAEO,SAAS,mBAAmB,IAAqB;IACtD,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACvB,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE;YACtB,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,EAAE;QACxB;QACA,GAAG,CAAC,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC;QACvB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,sBAAsB,SAA0B,EAAE,YAAoB;IACpF,MAAM,YAAY;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAEjE,OAAO,UAAU,GAAG,CAAC,CAAC,SAAS,eAAiB,CAAC;YAC/C,MAAM,QAAQ,OAAO;YACrB,MAAM,UAAU,GAAG,CAAC,CAAC,UAAU,gBAAkB,CAAC;oBAChD,MAAM;oBACN,GAAG,QAAQ,SAAS,CAAC,SAA2C;oBAChE,OAAO,sBAAsB,cAAc,eAAe;gBAC5D,CAAC;YACD,OAAO,sBAAsB,cAAc,eAAe;QAC5D,CAAC;AACH;AAEA,SAAS,sBAAsB,GAAW,EAAE,MAAc;IACxD,kCAAkC;IAClC,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;IAC3C,MAAM,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS;IACvC,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI;IACxB,MAAM,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI;IAChC,MAAM,IAAI,CAAC,MAAM,QAAQ,IAAI;IAC7B,OAAO,MAAM,CAAC,YAAY,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,UAC1D,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,QAClC,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AACxD", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport BreederChart from \"@/components/BreederChart\";\r\nimport { processChartData, groupDataByBreeder, BREEDERS, getBreederColor } from \"@/utils/dataProcessor\";\r\n\r\nexport default function Home() {\r\n  // Adatok feldolgozása\r\n  const erettData = processChartData('érett');\r\n  const romloData = processChartData('romló');\r\n\r\n  const erettGrouped = groupDataByBreeder(erettData);\r\n  const romloGrouped = groupDataByBreeder(romloData);\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-6\">\r\n      <div className=\"max-w-[1920px] mx-auto space-y-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center space-y-2\">\r\n          <h1 className=\"text-3xl sm:text-4xl lg:text-5xl font-bold text-foreground\">\r\n            🍅 Univer 2025 Dashboard\r\n          </h1>\r\n          <p className=\"text-base sm:text-lg text-muted-foreground\">\r\n            Tövön tarthatóság elemzés nemesítőh<PERSON><PERSON> szerint\r\n          </p>\r\n        </div>\r\n\r\n        {/* Bal-jobb oldali elrendezés */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n          {/* Bal oldal - Érett bogyó mennyisége szekció */}\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h2 className=\"text-2xl sm:text-3xl font-bold mb-2 text-foreground\">\r\n                Érett bogyó mennyisége (t/ha)\r\n              </h2>\r\n              <p className=\"text-sm sm:text-base text-muted-foreground\">\r\n                Az ép, érett bogyó mennyisége I. és II. szedés során\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              {BREEDERS.map((breeder) => {\r\n                const varieties = erettGrouped[breeder.name] || [];\r\n                if (varieties.length === 0) return null;\r\n\r\n                return (\r\n                  <div key={`erett-${breeder.name}`} className=\"w-full bg-card border border-border rounded-lg p-6\">\r\n                    <div className=\"mb-4\">\r\n                      <h3 className=\"text-lg sm:text-xl font-semibold flex items-center gap-3 text-foreground\">\r\n                        <div\r\n                          className=\"w-4 h-4 rounded-full\"\r\n                          style={{ backgroundColor: breeder.color }}\r\n                        />\r\n                        {breeder.name}\r\n                      </h3>\r\n                      <p className=\"text-sm text-muted-foreground\">\r\n                        {varieties.length} fajta adatai\r\n                      </p>\r\n                    </div>\r\n                    <BreederChart\r\n                      title=\"Érett bogyó mennyisége\"\r\n                      varieties={varieties}\r\n                      breederColor={breeder.color}\r\n                      breederName={breeder.name}\r\n                      allVarietiesData={erettData}\r\n                    />\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Jobb oldal - Romló bogyó mennyisége szekció */}\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h2 className=\"text-2xl sm:text-3xl font-bold mb-2 text-foreground\">\r\n                Romló bogyó mennyisége (t/ha)\r\n              </h2>\r\n              <p className=\"text-sm sm:text-base text-muted-foreground\">\r\n                A romló bogyó mennyisége I. és II. szedés során\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              {BREEDERS.map((breeder) => {\r\n                const varieties = romloGrouped[breeder.name] || [];\r\n                if (varieties.length === 0) return null;\r\n\r\n                return (\r\n                  <div key={`romlo-${breeder.name}`} className=\"w-full bg-card border border-border rounded-lg p-6\">\r\n                    <div className=\"mb-4\">\r\n                      <h3 className=\"text-lg sm:text-xl font-semibold flex items-center gap-3 text-foreground\">\r\n                        <div\r\n                          className=\"w-4 h-4 rounded-full\"\r\n                          style={{ backgroundColor: breeder.color }}\r\n                        />\r\n                        {breeder.name}\r\n                      </h3>\r\n                      <p className=\"text-sm text-muted-foreground\">\r\n                        {varieties.length} fajta adatai\r\n                      </p>\r\n                    </div>\r\n                    <BreederChart\r\n                      title=\"Romló bogyó mennyisége\"\r\n                      varieties={varieties}\r\n                      breederColor={breeder.color}\r\n                      breederName={breeder.name}\r\n                      allVarietiesData={romloData}\r\n                    />\r\n                  </div>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-12 pt-8 border-t border-border text-center\">\r\n          <p className=\"text-sm text-muted-foreground\">\r\n            🍅 Paradicsom fajtakísérlet - 2025 © Minden jog fenntartva\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,sBAAsB;IACtB,MAAM,YAAY,IAAA,iJAAgB,EAAC;IACnC,MAAM,YAAY,IAAA,iJAAgB,EAAC;IAEnC,MAAM,eAAe,IAAA,mJAAkB,EAAC;IACxC,MAAM,eAAe,IAAA,mJAAkB,EAAC;IAExC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA6D;;;;;;sCAG3E,8OAAC;4BAAE,WAAU;sCAA6C;;;;;;;;;;;;8BAM5D,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;8CACZ,yIAAQ,CAAC,GAAG,CAAC,CAAC;wCACb,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;wCAClD,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO;wCAEnC,qBACE,8OAAC;4CAAkC,WAAU;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,QAAQ,KAAK;oEAAC;;;;;;gEAEzC,QAAQ,IAAI;;;;;;;sEAEf,8OAAC;4DAAE,WAAU;;gEACV,UAAU,MAAM;gEAAC;;;;;;;;;;;;;8DAGtB,8OAAC,6IAAY;oDACX,OAAM;oDACN,WAAW;oDACX,cAAc,QAAQ,KAAK;oDAC3B,aAAa,QAAQ,IAAI;oDACzB,kBAAkB;;;;;;;2CAlBZ,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;oCAsBrC;;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDAA6C;;;;;;;;;;;;8CAK5D,8OAAC;oCAAI,WAAU;8CACZ,yIAAQ,CAAC,GAAG,CAAC,CAAC;wCACb,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;wCAClD,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO;wCAEnC,qBACE,8OAAC;4CAAkC,WAAU;;8DAC3C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;;8EACZ,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,QAAQ,KAAK;oEAAC;;;;;;gEAEzC,QAAQ,IAAI;;;;;;;sEAEf,8OAAC;4DAAE,WAAU;;gEACV,UAAU,MAAM;gEAAC;;;;;;;;;;;;;8DAGtB,8OAAC,6IAAY;oDACX,OAAM;oDACN,WAAW;oDACX,cAAc,QAAQ,KAAK;oDAC3B,aAAa,QAAQ,IAAI;oDACzB,kBAAkB;;;;;;;2CAlBZ,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;oCAsBrC;;;;;;;;;;;;;;;;;;8BAMN,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;;;;;;AAOvD", "debugId": null}}]}