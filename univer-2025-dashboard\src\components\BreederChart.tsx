'use client';

import React, { useEffect, useMemo } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { ProcessedData } from '@/utils/dataProcessor';
import { useTheme } from './ThemeProvider';

interface BreederChartProps {
  title: string;
  varieties: ProcessedData[];
  breederColor: string;
  breederName: string;
  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez
}

const BreederChart: React.FC<BreederChartProps> = ({
  title,
  varieties,
  breederColor,
  breederName,
  allVarietiesData = []
}) => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const { theme } = useTheme();

  // Modulok betöltése komponens betöltéskor (egyszerűsített)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Egyszerűen beállítjuk a Highcharts alapbeállításokat
      // Az export funkciók automatikusan működnek
      console.log('Highcharts initialized with export support');
    }
  }, []);

  // Dinamikus színek a téma alapján - külön színek sötét és világos módhoz
  const themeColors = useMemo(() => {
    if (theme === 'dark') {
      // SÖTÉT MÓD - Világos színek sötét háttéren
      return {
        background: 'transparent',
        titleColor: '#f8fafc',           // Tiszta fehér címek
        subtitleColor: '#cbd5e1',        // Világos szürke alcímek
        labelColor: '#94a3b8',           // Közepes világos szürke labelek
        gridLineColor: '#475569',        // Sötét szürke vonalak
        lineColor: '#475569',            // Sötét szürke tengelyek
        crosshairColor: 'rgba(248, 250, 252, 0.4)', // Világos crosshair
        plotBandColor: 'rgba(248, 250, 252, 0.08)',
        plotBandColorAlt: 'rgba(248, 250, 252, 0.15)',
        tooltipBg: 'rgba(15, 23, 42, 0.95)',        // Sötét tooltip háttér
        tooltipBorder: '#475569',                    // Sötét keret
        tooltipText: '#f8fafc',                      // Világos tooltip szöveg
        exportButtonBg: 'rgba(51, 65, 85, 0.9)',
        exportButtonHover: 'rgba(71, 85, 105, 0.95)',
        exportButtonStroke: '#f8fafc'
      };
    } else {
      // VILÁGOS MÓD - Sötét színek világos háttéren
      return {
        background: 'transparent',
        titleColor: '#0f172a',           // Mély sötét címek
        subtitleColor: '#334155',        // Sötét szürke alcímek
        labelColor: '#64748b',           // Közepes sötét szürke labelek
        gridLineColor: '#e2e8f0',        // Világos szürke vonalak
        lineColor: '#e2e8f0',            // Világos szürke tengelyek
        crosshairColor: 'rgba(15, 23, 42, 0.4)',    // Sötét crosshair
        plotBandColor: 'rgba(15, 23, 42, 0.06)',
        plotBandColorAlt: 'rgba(15, 23, 42, 0.12)',
        tooltipBg: 'rgba(255, 255, 255, 0.95)',     // Világos tooltip háttér
        tooltipBorder: '#e2e8f0',                    // Világos keret
        tooltipText: '#0f172a',                      // Sötét tooltip szöveg
        exportButtonBg: 'rgba(248, 250, 252, 0.9)',
        exportButtonHover: 'rgba(226, 232, 240, 0.95)',
        exportButtonStroke: '#0f172a'
      };
    }
  }, [theme]);
  // Színárnyalatok generálása a fajtákhoz
  const generateColorShades = (baseColor: string, count: number): string[] => {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      // Ha WALLER fajtáról van szó, akkor zöld színt használunk
      if (varieties[i]?.variety === 'WALLER') {
        colors.push('#16a34a'); // Zöld szín a WALLER fajtának
      } else {
        const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);
        colors.push(adjustColorBrightness(baseColor, factor));
      }
    }
    return colors;
  };

  const adjustColorBrightness = (hex: string, factor: number): string => {
    const num = parseInt(hex.replace('#', ''), 16);
    const R = Math.round((num >> 16) * factor);
    const G = Math.round(((num >> 8) & 0x00FF) * factor);
    const B = Math.round((num & 0x0000FF) * factor);
    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');
  };

  const colors = generateColorShades(breederColor, varieties.length);

  // Adatok előkészítése Highcharts számára
  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];
  
  const series = varieties.map((variety, index) => ({
    type: 'column' as const,
    name: variety.variety,
    data: categories.map(location =>
      variety.locations[location as keyof typeof variety.locations]
    ),
    color: colors[index]
  }));

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: themeColors.background,
      style: {
        fontFamily: 'var(--font-geist-sans)'
      },
      animation: false
    },
    title: {
      text: `${breederName}`,
      style: {
        color: themeColors.titleColor,
        fontSize: '18px',
        fontWeight: '600'
      }
    },
    subtitle: {
      text: title,
      style: {
        color: themeColors.subtitleColor,
        fontSize: '14px'
      }
    },
    xAxis: {
      categories: categories,
      labels: {
        style: {
          color: themeColors.labelColor
        }
      },
      lineColor: themeColors.lineColor,
      tickColor: themeColors.lineColor,
      crosshair: {
        width: 1,
        color: themeColors.crosshairColor,
        dashStyle: 'Solid' as const
      },
      plotBands: [
        {
          from: -0.5,
          to: 1.5,
          color: themeColors.plotBandColor,
          label: {
            text: 'Mezőberény',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 1.5,
          to: 3.5,
          color: themeColors.plotBandColorAlt,
          label: {
            text: 'Csabacsűd',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 3.5,
          to: 5.5,
          color: themeColors.plotBandColor,
          label: {
            text: 'Lakitelek',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        }
      ]
    },
    yAxis: {
      title: {
        text: 't/ha',
        style: {
          color: themeColors.labelColor
        }
      },
      labels: {
        style: {
          color: themeColors.labelColor
        }
      },
      gridLineColor: themeColors.gridLineColor
    },
    legend: {
      enabled: true,
      itemStyle: {
        color: themeColors.labelColor
      },
      itemHoverStyle: {
        color: themeColors.titleColor
      }
    },
    plotOptions: {
      column: {
        animation: false,
        borderWidth: 0,
        borderRadius: 3,
        groupPadding: 0.1,
        pointPadding: 0.05,
        dataLabels: {
          enabled: false
        },
        states: {
          hover: {
            brightness: 0.2,
            borderColor: themeColors.titleColor,
            borderWidth: 2
          },
          inactive: {
            opacity: 0.3
          }
        },
        cursor: 'pointer',
        point: {
          events: {
            mouseOver: function() {
              const chart = this.series.chart;
              const point = this;
              const varietyName = point.series.name;

              // Kiemeljük az összes ugyanolyan fajta oszlopot
              chart.series.forEach((series: any) => {
                if (series.name === varietyName) {
                  // Kiemeljük az aktív fajta oszlopait
                  series.points.forEach((p: any) => {
                    p.update({
                      color: Highcharts.color(series.color).brighten(0.2).get(),
                      borderColor: '#ffffff',
                      borderWidth: 2
                    }, false);
                  });
                  series.update({
                    opacity: 1
                  }, false);
                } else {
                  // Elhalványítjuk a többi fajtát
                  series.update({
                    opacity: 0.3
                  }, false);
                  series.points.forEach((p: any) => {
                    p.update({
                      opacity: 0.3
                    }, false);
                  });
                }
              });

              chart.redraw();
            },
            mouseOut: function() {
              const chart = this.series.chart;

              // Visszaállítjuk az eredeti állapotot
              chart.series.forEach((series: any) => {
                series.update({
                  opacity: 1
                }, false);
                series.points.forEach((p: any) => {
                  p.update({
                    color: series.color,
                    borderColor: undefined,
                    borderWidth: 0,
                    opacity: 1
                  }, false);
                });
              });

              chart.redraw();
            }
          }
        },
        stickyTracking: true
      },
      series: {
        states: {
          hover: {
            enabled: true
          },
          inactive: {
            opacity: 0.3
          }
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          enabled: true,
          theme: {
            fill: themeColors.exportButtonBg,
            stroke: themeColors.exportButtonStroke,
            r: 4,
            states: {
              hover: {
                fill: themeColors.exportButtonHover,
                stroke: themeColors.exportButtonStroke
              },
              select: {
                fill: themeColors.exportButtonHover,
                stroke: themeColors.exportButtonStroke
              }
            }
          } as any,
          menuItems: [
            'viewFullscreen',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadSVG'
          ],
          x: -10,
          y: 10
        }
      }
    },
    navigation: {
      buttonOptions: {
        enabled: true
      }
    },
    tooltip: {
      enabled: true,
      backgroundColor: themeColors.tooltipBg,
      borderColor: themeColors.tooltipBorder,
      borderRadius: 8,
      style: {
        color: themeColors.tooltipText,
        fontSize: '12px'
      },
      useHTML: true,
      positioner: function(this: any, labelWidth: number) {
        const chart = this.chart;
        const chartWidth = chart.chartWidth;

        // Tooltip a cím és a fullscreen gomb között, középen
        const x = (chartWidth / 2) - (labelWidth / 2); // Horizontálisan középre
        const y = 50; // A cím alatt, de a fullscreen gomb felett

        return { x, y };
      },
      formatter: function(this: any) {
        const point = this.point;
        const series = this.series;
        const chart = this.series.chart;

        // Megkeressük az összes ugyanolyan fajta adatait
        let varietyData: any[] = [];
        let totalValue = 0;
        let validCount = 0;

        chart.series.forEach((s: any) => {
          if (s.name === series.name) {
            s.points.forEach((p: any) => {
              if (p.y !== null && p.y !== undefined && p.y > 0) {
                varietyData.push({
                  location: p.category,
                  value: p.y,
                  seriesName: s.name,
                  color: s.color
                });
                totalValue += p.y;
                validCount++;
              }
            });
          }
        });

        // Átlag számítása
        const averageValue = validCount > 0 ? totalValue / validCount : 0;

        // Tooltip HTML összeállítása - téma-alapú színekkel
        let tooltipHtml = `<div style="width: 200px; max-height: 180px; display: flex; flex-direction: column; color: ${themeColors.tooltipText};">`;

        // Fejléc - téma-alapú színekkel
        const headerBorderColor = theme === 'dark' ? '#22c55e' : '#16a34a';
        tooltipHtml += `<div style="flex-shrink: 0; font-weight: bold; margin-bottom: 3px; font-size: 11px; color: ${themeColors.tooltipText}; border-bottom: 1px solid ${headerBorderColor}; padding-bottom: 2px;">${series.name}</div>`;

        // Scroll-ozható tartalom - kompakt
        tooltipHtml += `<div style="flex: 1; overflow-y: auto; max-height: 120px; margin-bottom: 3px;">`;

        // Összes helyszín adatai - téma-specifikus színekkel
        varietyData.forEach((data) => {
          const isCurrentPoint = data.location === point.category;

          // Téma-specifikus színek
          const bgColor = isCurrentPoint ?
            (theme === 'dark' ? 'rgba(34, 197, 94, 0.15)' : 'rgba(22, 163, 74, 0.1)') :
            'transparent';
          const textColor = isCurrentPoint ?
            (theme === 'dark' ? '#22c55e' : '#16a34a') :
            (theme === 'dark' ? '#94a3b8' : '#64748b');
          const valueColor = isCurrentPoint ?
            (theme === 'dark' ? '#f8fafc' : '#0f172a') :
            (theme === 'dark' ? '#cbd5e1' : '#475569');
          const borderColor = isCurrentPoint ?
            (theme === 'dark' ? '#22c55e' : '#16a34a') :
            'transparent';

          tooltipHtml += `<div style="display: flex; align-items: center; margin: 1px 0; padding: 1px 2px; border-radius: 2px; background: ${bgColor}; border-left: 1px solid ${borderColor};">`;
          tooltipHtml += `<span style="width: 5px; height: 5px; background-color: ${data.color}; display: inline-block; margin-right: 3px; border-radius: 1px; flex-shrink: 0;"></span>`;
          tooltipHtml += `<span style="flex: 1; font-size: 10px; color: ${textColor}; font-weight: ${isCurrentPoint ? '600' : '400'};">${data.location}</span>`;
          tooltipHtml += `<span style="font-weight: bold; color: ${valueColor}; font-size: 10px; margin-left: 2px;">${data.value.toFixed(1)}</span>`;
          tooltipHtml += `</div>`;
        });

        tooltipHtml += `</div>`;

        // Összeg - téma-specifikus színekkel
        const summaryBorderColor = theme === 'dark' ? 'rgba(248, 250, 252, 0.2)' : 'rgba(15, 23, 42, 0.2)';
        const summaryBgColor = theme === 'dark' ? 'rgba(34, 197, 94, 0.1)' : 'rgba(22, 163, 74, 0.05)';
        const avgBgColor = theme === 'dark' ? 'rgba(248, 250, 252, 0.1)' : 'rgba(15, 23, 42, 0.05)';
        const avgColor = theme === 'dark' ? '#22c55e' : '#16a34a';
        const avgLabelColor = theme === 'dark' ? '#f8fafc' : '#0f172a';

        tooltipHtml += `<div style="flex-shrink: 0; border-top: 1px solid ${summaryBorderColor}; padding: 2px; background: ${summaryBgColor}; border-radius: 2px;">`;
        tooltipHtml += `<div style="display: flex; align-items: center; justify-content: space-between; font-weight: bold;">`;
        tooltipHtml += `<span style="font-size: 9px; color: ${avgLabelColor};">Átlag:</span>`;
        tooltipHtml += `<span style="font-size: 10px; color: ${avgColor}; background: ${avgBgColor}; padding: 1px 2px; border-radius: 2px;">${averageValue.toFixed(1)}</span>`;
        tooltipHtml += `</div>`;
        tooltipHtml += `</div>`;

        tooltipHtml += `</div>`;

        return tooltipHtml;
      }
    }
  };

  return (
    <div className="w-full h-96 relative">
      <button
        onClick={() => {
          if (chartRef.current) {
            if (chartRef.current.requestFullscreen) {
              chartRef.current.requestFullscreen();
            } else if ((chartRef.current as any).webkitRequestFullscreen) {
              (chartRef.current as any).webkitRequestFullscreen();
            } else if ((chartRef.current as any).mozRequestFullScreen) {
              (chartRef.current as any).mozRequestFullScreen();
            } else if ((chartRef.current as any).msRequestFullscreen) {
              (chartRef.current as any).msRequestFullscreen();
            }
          }
        }}
        className="absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg"
        title="Teljes képernyő"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
          />
        </svg>
      </button>
      <div ref={chartRef} className="w-full h-96">
        <HighchartsReact
          highcharts={Highcharts}
          options={options}
        />
      </div>
    </div>
  );
};

export default BreederChart;
