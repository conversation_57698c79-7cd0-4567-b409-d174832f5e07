'use client';

import React, { useEffect, useMemo, useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { ProcessedData } from '@/utils/dataProcessor';
import { useTheme } from './ThemeProvider';

interface BreederChartProps {
  title: string;
  varieties: ProcessedData[];
  breederColor: string;
  breederName: string;
  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez
}

interface SelectedBreederDataPoint {
  variety: string;
  location: string;
  value: number;
  seriesColor: string;
  allLocationData: { location: string; value: number }[];
}

// Információs panel komponens a BreederChart-hoz
const BreederDataInfoPanel: React.FC<{
  selectedData: SelectedBreederDataPoint | null;
  varieties: ProcessedData[];
  theme: string;
}> = ({ selectedData, varieties, theme }) => {
  if (!selectedData) {
    return (
      <div className="w-80 bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 ml-4 transition-all duration-300">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-foreground mb-2">Fajta adatok</h3>
          <p className="text-muted-foreground text-sm">
            Kattints egy oszlopra a részletes adatok megtekintéséhez
          </p>
        </div>
      </div>
    );
  }

  // Statisztikák számítása
  const allValues = selectedData.allLocationData.map(d => d.value);
  const maxValue = Math.max(...allValues);
  const minValue = Math.min(...allValues);
  const avgValue = allValues.reduce((sum, val) => sum + val, 0) / allValues.length;
  const percentageOfMax = (selectedData.value / maxValue) * 100;
  const isAboveAverage = selectedData.value > avgValue;

  // Helyszín nevek mapping
  const locationNames: { [key: string]: string } = {
    'M-I': 'Mezőberény I.',
    'M-II': 'Mezőberény II.',
    'Cs-I': 'Csabacsűd I.',
    'Cs-II': 'Csabacsűd II.',
    'L-I': 'Lakitelek I.',
    'L-II': 'Lakitelek II.'
  };

  return (
    <div className="w-80 bg-card/50 backdrop-blur-sm border border-border rounded-lg p-6 ml-4 transition-all duration-300">
      {/* Fejléc */}
      <div className="flex items-center mb-4">
        <div
          className="w-10 h-10 rounded-full flex items-center justify-center mr-3"
          style={{ backgroundColor: selectedData.seriesColor }}
        >
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-foreground">{selectedData.variety}</h3>
          <p className="text-xs text-muted-foreground">{locationNames[selectedData.location] || selectedData.location}</p>
        </div>
      </div>

      {/* Fő adatok */}
      <div className="space-y-4">
        <div className="bg-muted/50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-muted-foreground text-sm">Helyszín</span>
            <span className="text-foreground font-semibold">{locationNames[selectedData.location] || selectedData.location}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-muted-foreground text-sm">Érték</span>
            <span className="text-2xl font-bold text-foreground">{selectedData.value.toFixed(1)} t/ha</span>
          </div>
        </div>

        {/* Vizuális progress bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-muted-foreground">
            <span>Relatív érték</span>
            <span>{percentageOfMax.toFixed(1)}% a maximumból</span>
          </div>
          <div className="w-full bg-muted rounded-full h-3">
            <div
              className="h-3 rounded-full transition-all duration-500 ease-out"
              style={{
                width: `${percentageOfMax}%`,
                backgroundColor: selectedData.seriesColor
              }}
            ></div>
          </div>
        </div>

        {/* Statisztikák */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-muted/30 rounded-lg p-3 text-center">
            <div className="text-xs text-muted-foreground mb-1">Maximum</div>
            <div className="text-sm font-semibold text-foreground">{maxValue.toFixed(1)}</div>
          </div>
          <div className="bg-muted/30 rounded-lg p-3 text-center">
            <div className="text-xs text-muted-foreground mb-1">Átlag</div>
            <div className="text-sm font-semibold text-foreground">{avgValue.toFixed(1)}</div>
          </div>
        </div>

        {/* Összehasonlítás */}
        <div className={`rounded-lg p-3 border ${isAboveAverage ? 'bg-green-500/10 border-green-500/30' : 'bg-orange-500/10 border-orange-500/30'}`}>
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${isAboveAverage ? 'bg-green-500' : 'bg-orange-500'}`}></div>
            <span className={`text-xs ${isAboveAverage ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
              {isAboveAverage ? 'Átlag feletti érték' : 'Átlag alatti érték'}
            </span>
          </div>
          <div className={`text-xs mt-1 ${isAboveAverage ? 'text-green-600 dark:text-green-400' : 'text-orange-600 dark:text-orange-400'}`}>
            {isAboveAverage ? '+' : ''}{(selectedData.value - avgValue).toFixed(1)} az átlagtól
          </div>
        </div>

        {/* Összes helyszín adatai */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-foreground">Összes helyszín:</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {selectedData.allLocationData.map((data, index) => (
              <div
                key={index}
                className={`flex justify-between items-center p-2 rounded text-xs ${
                  data.location === selectedData.location
                    ? 'bg-primary/20 border border-primary/30'
                    : 'bg-muted/30'
                }`}
              >
                <span className="text-muted-foreground">{locationNames[data.location] || data.location}</span>
                <span className="font-medium text-foreground">{data.value.toFixed(1)}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

const BreederChart: React.FC<BreederChartProps> = ({
  title,
  varieties,
  breederColor,
  breederName,
  allVarietiesData = []
}) => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const { theme } = useTheme();
  const [selectedData, setSelectedData] = useState<SelectedBreederDataPoint | null>(null);

  // Modulok betöltése komponens betöltéskor (egyszerűsített)
  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Egyszerűen beállítjuk a Highcharts alapbeállításokat
      // Az export funkciók automatikusan működnek
      console.log('Highcharts initialized with export support');
    }
  }, []);

  // Dinamikus színek a téma alapján - külön színek sötét és világos módhoz
  const themeColors = useMemo(() => {
    if (theme === 'dark') {
      // SÖTÉT MÓD - Világos színek sötét háttéren
      return {
        background: 'transparent',
        titleColor: '#f8fafc',           // Tiszta fehér címek
        subtitleColor: '#cbd5e1',        // Világos szürke alcímek
        labelColor: '#94a3b8',           // Közepes világos szürke labelek
        gridLineColor: '#475569',        // Sötét szürke vonalak
        lineColor: '#475569',            // Sötét szürke tengelyek
        crosshairColor: 'rgba(248, 250, 252, 0.4)', // Világos crosshair
        plotBandColor: 'rgba(248, 250, 252, 0.08)',
        plotBandColorAlt: 'rgba(248, 250, 252, 0.15)',
        tooltipBg: 'rgba(15, 23, 42, 0.95)',        // Sötét tooltip háttér
        tooltipBorder: '#475569',                    // Sötét keret
        tooltipText: '#f8fafc',                      // Világos tooltip szöveg
        exportButtonBg: 'rgba(51, 65, 85, 0.9)',
        exportButtonHover: 'rgba(71, 85, 105, 0.95)',
        exportButtonStroke: '#f8fafc'
      };
    } else {
      // VILÁGOS MÓD - Sötét színek világos háttéren
      return {
        background: 'transparent',
        titleColor: '#0f172a',           // Mély sötét címek
        subtitleColor: '#334155',        // Sötét szürke alcímek
        labelColor: '#64748b',           // Közepes sötét szürke labelek
        gridLineColor: '#e2e8f0',        // Világos szürke vonalak
        lineColor: '#e2e8f0',            // Világos szürke tengelyek
        crosshairColor: 'rgba(15, 23, 42, 0.4)',    // Sötét crosshair
        plotBandColor: 'rgba(15, 23, 42, 0.06)',
        plotBandColorAlt: 'rgba(15, 23, 42, 0.12)',
        tooltipBg: 'rgba(255, 255, 255, 0.95)',     // Világos tooltip háttér
        tooltipBorder: '#e2e8f0',                    // Világos keret
        tooltipText: '#0f172a',                      // Sötét tooltip szöveg
        exportButtonBg: 'rgba(248, 250, 252, 0.9)',
        exportButtonHover: 'rgba(226, 232, 240, 0.95)',
        exportButtonStroke: '#0f172a'
      };
    }
  }, [theme]);
  // Színárnyalatok generálása a fajtákhoz
  const generateColorShades = (baseColor: string, count: number): string[] => {
    const colors: string[] = [];
    for (let i = 0; i < count; i++) {
      // Ha WALLER fajtáról van szó, akkor zöld színt használunk
      if (varieties[i]?.variety === 'WALLER') {
        colors.push('#16a34a'); // Zöld szín a WALLER fajtának
      } else {
        const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);
        colors.push(adjustColorBrightness(baseColor, factor));
      }
    }
    return colors;
  };

  const adjustColorBrightness = (hex: string, factor: number): string => {
    const num = parseInt(hex.replace('#', ''), 16);
    const R = Math.round((num >> 16) * factor);
    const G = Math.round(((num >> 8) & 0x00FF) * factor);
    const B = Math.round((num & 0x0000FF) * factor);
    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');
  };

  const colors = generateColorShades(breederColor, varieties.length);

  // Adatok előkészítése Highcharts számára
  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];
  
  const series = varieties.map((variety, index) => ({
    type: 'column' as const,
    name: variety.variety,
    data: categories.map(location =>
      variety.locations[location as keyof typeof variety.locations]
    ),
    color: colors[index]
  }));

  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: themeColors.background,
      style: {
        fontFamily: 'var(--font-geist-sans)'
      },
      animation: false
    },
    title: {
      text: `${breederName}`,
      style: {
        color: themeColors.titleColor,
        fontSize: '18px',
        fontWeight: '600'
      }
    },
    subtitle: {
      text: title,
      style: {
        color: themeColors.subtitleColor,
        fontSize: '14px'
      }
    },
    xAxis: {
      categories: categories,
      labels: {
        style: {
          color: themeColors.labelColor
        }
      },
      lineColor: themeColors.lineColor,
      tickColor: themeColors.lineColor,
      crosshair: {
        width: 1,
        color: themeColors.crosshairColor,
        dashStyle: 'Solid' as const
      },
      plotBands: [
        {
          from: -0.5,
          to: 1.5,
          color: themeColors.plotBandColor,
          label: {
            text: 'Mezőberény',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 1.5,
          to: 3.5,
          color: themeColors.plotBandColorAlt,
          label: {
            text: 'Csabacsűd',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        },
        {
          from: 3.5,
          to: 5.5,
          color: themeColors.plotBandColor,
          label: {
            text: 'Lakitelek',
            style: {
              color: themeColors.labelColor,
              fontSize: '12px'
            },
            align: 'center'
          }
        }
      ]
    },
    yAxis: {
      title: {
        text: 't/ha',
        style: {
          color: themeColors.labelColor
        }
      },
      labels: {
        style: {
          color: themeColors.labelColor
        }
      },
      gridLineColor: themeColors.gridLineColor
    },
    legend: {
      enabled: true,
      itemStyle: {
        color: themeColors.labelColor
      },
      itemHoverStyle: {
        color: themeColors.titleColor
      }
    },
    plotOptions: {
      column: {
        animation: false,
        borderWidth: 0,
        borderRadius: 3,
        groupPadding: 0.1,
        pointPadding: 0.05,
        dataLabels: {
          enabled: false
        },
        states: {
          hover: {
            brightness: 0.2,
            borderColor: themeColors.titleColor,
            borderWidth: 2
          },
          inactive: {
            opacity: 0.3
          }
        },
        cursor: 'pointer',
        point: {
          events: {
            click: function(this: Highcharts.Point) {
              const point = this as any;
              const series = point.series;
              const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];

              // Összegyűjtjük az adott fajta összes helyszínének adatait
              const allLocationData = categories.map(location => {
                const locationIndex = categories.indexOf(location);
                const value = series.data[locationIndex] ? series.data[locationIndex].y : 0;
                return {
                  location,
                  value
                };
              });

              setSelectedData({
                variety: series.name,
                location: point.category,
                value: point.y,
                seriesColor: series.color,
                allLocationData
              });
            },
            mouseOver: function() {
              const chart = this.series.chart;
              const point = this;
              const varietyName = point.series.name;

              // Kiemeljük az összes ugyanolyan fajta oszlopot
              chart.series.forEach((series: any) => {
                if (series.name === varietyName) {
                  // Kiemeljük az aktív fajta oszlopait
                  series.points.forEach((p: any) => {
                    p.update({
                      color: Highcharts.color(series.color).brighten(0.2).get(),
                      borderColor: '#ffffff',
                      borderWidth: 2
                    }, false);
                  });
                  series.update({
                    opacity: 1
                  }, false);
                } else {
                  // Elhalványítjuk a többi fajtát
                  series.update({
                    opacity: 0.3
                  }, false);
                  series.points.forEach((p: any) => {
                    p.update({
                      opacity: 0.3
                    }, false);
                  });
                }
              });

              chart.redraw();
            },
            mouseOut: function() {
              const chart = this.series.chart;

              // Visszaállítjuk az eredeti állapotot
              chart.series.forEach((series: any) => {
                series.update({
                  opacity: 1
                }, false);
                series.points.forEach((p: any) => {
                  p.update({
                    color: series.color,
                    borderColor: undefined,
                    borderWidth: 0,
                    opacity: 1
                  }, false);
                });
              });

              chart.redraw();
            }
          }
        },
        stickyTracking: true
      },
      series: {
        states: {
          hover: {
            enabled: true
          },
          inactive: {
            opacity: 0.3
          }
        }
      }
    },
    series: series,
    credits: {
      enabled: false
    },
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          enabled: true,
          theme: {
            fill: themeColors.exportButtonBg,
            stroke: themeColors.exportButtonStroke,
            r: 4,
            states: {
              hover: {
                fill: themeColors.exportButtonHover,
                stroke: themeColors.exportButtonStroke
              },
              select: {
                fill: themeColors.exportButtonHover,
                stroke: themeColors.exportButtonStroke
              }
            }
          } as any,
          menuItems: [
            'viewFullscreen',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadSVG'
          ],
          x: -10,
          y: 10
        }
      }
    },
    navigation: {
      buttonOptions: {
        enabled: true
      }
    },
    tooltip: {
      enabled: false
    }
  };

  return (
    <div className="w-full flex gap-4">
      <div className="flex-1 h-96 relative">
        <button
          onClick={() => {
            if (chartRef.current) {
              if (chartRef.current.requestFullscreen) {
                chartRef.current.requestFullscreen();
              } else if ((chartRef.current as any).webkitRequestFullscreen) {
                (chartRef.current as any).webkitRequestFullscreen();
              } else if ((chartRef.current as any).mozRequestFullScreen) {
                (chartRef.current as any).mozRequestFullScreen();
              } else if ((chartRef.current as any).msRequestFullscreen) {
                (chartRef.current as any).msRequestFullscreen();
              }
            }
          }}
          className="absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg"
          title="Teljes képernyő"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            />
          </svg>
        </button>
        <div ref={chartRef} className="w-full h-96">
          <HighchartsReact
            highcharts={Highcharts}
            options={options}
          />
        </div>
      </div>
      <BreederDataInfoPanel selectedData={selectedData} varieties={varieties} theme={theme} />
    </div>
  );
};

export default BreederChart;
