{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/BreederChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport Highcharts from 'highcharts';\r\nimport HighchartsReact from 'highcharts-react-official';\r\nimport { ProcessedData } from '@/utils/dataProcessor';\r\n\r\ninterface BreederChartProps {\r\n  title: string;\r\n  varieties: ProcessedData[];\r\n  breederColor: string;\r\n  breederName: string;\r\n  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez\r\n}\r\n\r\nconst BreederChart: React.FC<BreederChartProps> = ({\r\n  title,\r\n  varieties,\r\n  breederColor,\r\n  breederName,\r\n  allVarietiesData = []\r\n}) => {\r\n  const chartRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  // Modulok betöltése komponens betöltéskor (egyszerűsített)\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Egyszerűen beállítjuk a Highcharts alapbeállításokat\r\n      // Az export funkciók automatikusan működnek\r\n      console.log('Highcharts initialized with export support');\r\n    }\r\n  }, []);\r\n  // Színárnyalatok generálása a fajtákhoz\r\n  const generateColorShades = (baseColor: string, count: number): string[] => {\r\n    const colors: string[] = [];\r\n    for (let i = 0; i < count; i++) {\r\n      // Ha WALLER fajtáról van szó, akkor zöld színt használunk\r\n      if (varieties[i]?.variety === 'WALLER') {\r\n        colors.push('#16a34a'); // Zöld szín a WALLER fajtának\r\n      } else {\r\n        const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);\r\n        colors.push(adjustColorBrightness(baseColor, factor));\r\n      }\r\n    }\r\n    return colors;\r\n  };\r\n\r\n  const adjustColorBrightness = (hex: string, factor: number): string => {\r\n    const num = parseInt(hex.replace('#', ''), 16);\r\n    const R = Math.round((num >> 16) * factor);\r\n    const G = Math.round(((num >> 8) & 0x00FF) * factor);\r\n    const B = Math.round((num & 0x0000FF) * factor);\r\n    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');\r\n  };\r\n\r\n  const colors = generateColorShades(breederColor, varieties.length);\r\n\r\n  // Adatok előkészítése Highcharts számára\r\n  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\r\n  \r\n  const series = varieties.map((variety, index) => ({\r\n    type: 'column' as const,\r\n    name: variety.variety,\r\n    data: categories.map(location =>\r\n      variety.locations[location as keyof typeof variety.locations]\r\n    ),\r\n    color: colors[index]\r\n  }));\r\n\r\n  const options: Highcharts.Options = {\r\n    chart: {\r\n      type: 'column',\r\n      backgroundColor: 'transparent',\r\n      style: {\r\n        fontFamily: 'var(--font-geist-sans)'\r\n      }\r\n    },\r\n    title: {\r\n      text: `${breederName}`,\r\n      style: {\r\n        color: '#ffffff',\r\n        fontSize: '18px',\r\n        fontWeight: '600'\r\n      }\r\n    },\r\n    subtitle: {\r\n      text: title,\r\n      style: {\r\n        color: '#a1a1aa',\r\n        fontSize: '14px'\r\n      }\r\n    },\r\n    xAxis: {\r\n      categories: categories,\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      lineColor: '#3f3f46',\r\n      tickColor: '#3f3f46',\r\n      crosshair: {\r\n        width: 1,\r\n        color: 'rgba(255, 255, 255, 0.3)',\r\n        dashStyle: 'Solid' as const\r\n      },\r\n      plotBands: [\r\n        {\r\n          from: -0.5,\r\n          to: 1.5,\r\n          color: 'rgba(255, 255, 255, 0.02)',\r\n          label: {\r\n            text: 'Mezőberény',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 1.5,\r\n          to: 3.5,\r\n          color: 'rgba(255, 255, 255, 0.05)',\r\n          label: {\r\n            text: 'Csabacsűd',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 3.5,\r\n          to: 5.5,\r\n          color: 'rgba(255, 255, 255, 0.02)',\r\n          label: {\r\n            text: 'Lakitelek',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    yAxis: {\r\n      title: {\r\n        text: 't/ha',\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      gridLineColor: '#3f3f46'\r\n    },\r\n    legend: {\r\n      enabled: true,\r\n      itemStyle: {\r\n        color: '#a1a1aa'\r\n      },\r\n      itemHoverStyle: {\r\n        color: '#ffffff'\r\n      }\r\n    },\r\n    plotOptions: {\r\n      column: {\r\n        borderWidth: 0,\r\n        borderRadius: 3,\r\n        groupPadding: 0.1,\r\n        pointPadding: 0.05,\r\n        dataLabels: {\r\n          enabled: false\r\n        },\r\n        states: {\r\n          hover: {\r\n            brightness: 0.2,\r\n            borderColor: '#ffffff',\r\n            borderWidth: 2\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        },\r\n        cursor: 'pointer',\r\n        point: {\r\n          events: {\r\n            mouseOver: function() {\r\n              const chart = this.series.chart;\r\n              const point = this;\r\n              const varietyName = point.series.name;\r\n\r\n              // Kiemeljük az összes ugyanolyan fajta oszlopot\r\n              chart.series.forEach((series: any) => {\r\n                if (series.name === varietyName) {\r\n                  // Kiemeljük az aktív fajta oszlopait\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      color: Highcharts.color(series.color).brighten(0.2).get(),\r\n                      borderColor: '#ffffff',\r\n                      borderWidth: 2\r\n                    }, false);\r\n                  });\r\n                  series.update({\r\n                    opacity: 1\r\n                  }, false);\r\n                } else {\r\n                  // Elhalványítjuk a többi fajtát\r\n                  series.update({\r\n                    opacity: 0.3\r\n                  }, false);\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      opacity: 0.3\r\n                    }, false);\r\n                  });\r\n                }\r\n              });\r\n\r\n              chart.redraw();\r\n            },\r\n            mouseOut: function() {\r\n              const chart = this.series.chart;\r\n\r\n              // Visszaállítjuk az eredeti állapotot\r\n              chart.series.forEach((series: any) => {\r\n                series.update({\r\n                  opacity: 1\r\n                }, false);\r\n                series.points.forEach((p: any) => {\r\n                  p.update({\r\n                    color: series.color,\r\n                    borderColor: undefined,\r\n                    borderWidth: 0,\r\n                    opacity: 1\r\n                  }, false);\r\n                });\r\n              });\r\n\r\n              chart.redraw();\r\n            }\r\n          }\r\n        },\r\n        stickyTracking: true\r\n      },\r\n      series: {\r\n        states: {\r\n          hover: {\r\n            enabled: true\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        }\r\n      }\r\n    },\r\n    series: series,\r\n    credits: {\r\n      enabled: false\r\n    },\r\n    exporting: {\r\n      enabled: true,\r\n      buttons: {\r\n        contextButton: {\r\n          enabled: true,\r\n          theme: {\r\n            fill: 'rgba(55, 65, 81, 0.9)',\r\n            stroke: '#ffffff',\r\n            r: 4,\r\n            states: {\r\n              hover: {\r\n                fill: 'rgba(75, 85, 99, 0.95)',\r\n                stroke: '#ffffff'\r\n              },\r\n              select: {\r\n                fill: 'rgba(107, 114, 128, 0.95)',\r\n                stroke: '#ffffff'\r\n              }\r\n            }\r\n          } as any,\r\n          menuItems: [\r\n            'viewFullscreen',\r\n            'separator',\r\n            'downloadPNG',\r\n            'downloadJPEG',\r\n            'downloadSVG'\r\n          ],\r\n          x: -10,\r\n          y: 10\r\n        }\r\n      }\r\n    },\r\n    navigation: {\r\n      buttonOptions: {\r\n        enabled: true\r\n      }\r\n    },\r\n    tooltip: {\r\n      enabled: true,\r\n      backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n      borderColor: '#374151',\r\n      borderRadius: 8,\r\n      style: {\r\n        color: '#ffffff',\r\n        fontSize: '12px'\r\n      },\r\n      useHTML: true,\r\n      positioner: function(this: any, labelWidth: number, labelHeight: number, point: any) {\r\n        const chart = this.chart;\r\n        const plotLeft = chart.plotLeft;\r\n        const plotTop = chart.plotTop;\r\n        const plotHeight = chart.plotHeight;\r\n\r\n        // Konténer határok lekérdezése\r\n        const chartContainer = chart.container.parentElement;\r\n        const containerRect = chartContainer.getBoundingClientRect();\r\n        const containerHeight = containerRect.height;\r\n\r\n        // Tooltip a diagram bal oldalán jelenik meg - még balabbra\r\n        let x = plotLeft - labelWidth - 45; // Diagram bal szélétől 30px-re balra (nagyobb távolság)\r\n        let y = plotTop + (plotHeight / 2) - (labelHeight / 2); // Középen függőlegesen\r\n\r\n        // Biztosítjuk, hogy a tooltip ne menjen ki a konténerből\r\n        x = Math.max(5, x); // Legalább 5px-re a bal széltől\r\n        y = Math.max(5, Math.min(y, containerHeight - labelHeight - 5)); // Fentről/lentről ne lógjon ki\r\n\r\n        return { x, y };\r\n      },\r\n      formatter: function(this: any) {\r\n        const point = this.point;\r\n        const series = this.series;\r\n        const chart = this.series.chart;\r\n\r\n        // Megkeressük az összes ugyanolyan fajta adatait\r\n        let varietyData: any[] = [];\r\n        let totalValue = 0;\r\n        let validCount = 0;\r\n\r\n        chart.series.forEach((s: any) => {\r\n          if (s.name === series.name) {\r\n            s.points.forEach((p: any) => {\r\n              if (p.y !== null && p.y !== undefined && p.y > 0) {\r\n                varietyData.push({\r\n                  location: p.category,\r\n                  value: p.y,\r\n                  seriesName: s.name,\r\n                  color: s.color\r\n                });\r\n                totalValue += p.y;\r\n                validCount++;\r\n              }\r\n            });\r\n          }\r\n        });\r\n\r\n        // Átlag számítása\r\n        const averageValue = validCount > 0 ? totalValue / validCount : 0;\r\n\r\n        // Tooltip HTML összeállítása - optimális szélesség\r\n        let tooltipHtml = `<div style=\"width: 120px; max-height: 250px; display: flex; flex-direction: column;\">`;\r\n\r\n        // Fejléc - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex-shrink: 0; font-weight: bold; margin-bottom: 4px; font-size: 12px; color: #ffffff; border-bottom: 1px solid #10b981; padding-bottom: 3px;\">${series.name}</div>`;\r\n\r\n        // Scroll-ozható tartalom - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex: 1; overflow-y: auto; max-height: 150px; margin-bottom: 4px;\">`;\r\n\r\n        // Összes helyszín adatai - nagyobb betűk\r\n        varietyData.forEach((data, index) => {\r\n          const isCurrentPoint = data.location === point.category;\r\n          const bgColor = isCurrentPoint ? 'rgba(16, 185, 129, 0.15)' : 'transparent';\r\n          const textColor = isCurrentPoint ? '#10b981' : '#d1d5db';\r\n          const valueColor = isCurrentPoint ? '#ffffff' : '#9ca3af';\r\n\r\n          tooltipHtml += `<div style=\"display: flex; align-items: center; margin: 1px 0; padding: 2px 3px; border-radius: 2px; background: ${bgColor}; border-left: 1px solid ${isCurrentPoint ? '#10b981' : 'transparent'};\">`;\r\n          tooltipHtml += `<span style=\"width: 6px; height: 6px; background-color: ${data.color}; display: inline-block; margin-right: 4px; border-radius: 1px; flex-shrink: 0;\"></span>`;\r\n          tooltipHtml += `<span style=\"flex: 1; font-size: 11px; color: ${textColor}; font-weight: ${isCurrentPoint ? '600' : '400'};\">${data.location}</span>`;\r\n          tooltipHtml += `<span style=\"font-weight: bold; color: ${valueColor}; font-size: 11px; margin-left: 3px;\">${data.value.toFixed(1)}</span>`;\r\n          tooltipHtml += `</div>`;\r\n        });\r\n\r\n        tooltipHtml += `</div>`;\r\n\r\n        // Összeg - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex-shrink: 0; border-top: 1px solid rgba(255, 255, 255, 0.2); padding: 3px; background: rgba(16, 185, 129, 0.1); border-radius: 2px;\">`;\r\n        tooltipHtml += `<div style=\"display: flex; align-items: center; justify-content: space-between; font-weight: bold;\">`;\r\n        tooltipHtml += `<span style=\"font-size: 10px; color: #ffffff;\">Átlag:</span>`;\r\n        tooltipHtml += `<span style=\"font-size: 12px; color: #10b981; background: rgba(255, 255, 255, 0.1); padding: 1px 3px; border-radius: 2px;\">${averageValue.toFixed(1)}</span>`;\r\n        tooltipHtml += `</div>`;\r\n        tooltipHtml += `</div>`;\r\n\r\n        tooltipHtml += `</div>`;\r\n\r\n        return tooltipHtml;\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full h-96 relative\">\r\n      <button\r\n        onClick={() => {\r\n          if (chartRef.current) {\r\n            if (chartRef.current.requestFullscreen) {\r\n              chartRef.current.requestFullscreen();\r\n            } else if ((chartRef.current as any).webkitRequestFullscreen) {\r\n              (chartRef.current as any).webkitRequestFullscreen();\r\n            } else if ((chartRef.current as any).mozRequestFullScreen) {\r\n              (chartRef.current as any).mozRequestFullScreen();\r\n            } else if ((chartRef.current as any).msRequestFullscreen) {\r\n              (chartRef.current as any).msRequestFullscreen();\r\n            }\r\n          }\r\n        }}\r\n        className=\"absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg\"\r\n        title=\"Teljes képernyő\"\r\n      >\r\n        <svg\r\n          className=\"w-5 h-5\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth={2}\r\n            d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\r\n          />\r\n        </svg>\r\n      </button>\r\n      <div ref={chartRef} className=\"w-full h-96\">\r\n        <HighchartsReact\r\n          highcharts={Highcharts}\r\n          options={options}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BreederChart;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,SAAS,EACT,YAAY,EACZ,WAAW,EACX,mBAAmB,EAAE,EACtB;IACC,MAAM,WAAW,gNAAK,CAAC,MAAM,CAAiB;IAE9C,2DAA2D;IAC3D,IAAA,kNAAS,EAAC;QACR;;IAKF,GAAG,EAAE;IACL,wCAAwC;IACxC,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,0DAA0D;YAC1D,IAAI,SAAS,CAAC,EAAE,EAAE,YAAY,UAAU;gBACtC,OAAO,IAAI,CAAC,YAAY,8BAA8B;YACxD,OAAO;gBACL,MAAM,SAAS,MAAM,AAAC,IAAI,MAAO,KAAK,GAAG,CAAC,QAAQ,GAAG;gBACrD,OAAO,IAAI,CAAC,sBAAsB,WAAW;YAC/C;QACF;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI;QACnC,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,AAAC,OAAO,IAAK,MAAM,IAAI;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,QAAQ,IAAI;QACxC,OAAO,MAAM,CAAC,AAAC,KAAK,KAAO,KAAK,IAAK,CAAC,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;IACnE;IAEA,MAAM,SAAS,oBAAoB,cAAc,UAAU,MAAM;IAEjE,yCAAyC;IACzC,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAElE,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;YAChD,MAAM;YACN,MAAM,QAAQ,OAAO;YACrB,MAAM,WAAW,GAAG,CAAC,CAAA,WACnB,QAAQ,SAAS,CAAC,SAA2C;YAE/D,OAAO,MAAM,CAAC,MAAM;QACtB,CAAC;IAED,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,OAAO;gBACL,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM,GAAG,aAAa;YACtB,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF;QACA,UAAU;YACR,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,UAAU;YACZ;QACF;QACA,OAAO;YACL,YAAY;YACZ,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,WAAW;YACX,WAAW;YACX,WAAW;gBACT,OAAO;gBACP,OAAO;gBACP,WAAW;YACb;YACA,WAAW;gBACT;oBACE,MAAM,CAAC;oBACP,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;aACD;QACH;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,eAAe;QACjB;QACA,QAAQ;YACN,SAAS;YACT,WAAW;gBACT,OAAO;YACT;YACA,gBAAgB;gBACd,OAAO;YACT;QACF;QACA,aAAa;YACX,QAAQ;gBACN,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,YAAY;oBACV,SAAS;gBACX;gBACA,QAAQ;oBACN,OAAO;wBACL,YAAY;wBACZ,aAAa;wBACb,aAAa;oBACf;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;gBACA,QAAQ;gBACR,OAAO;oBACL,QAAQ;wBACN,WAAW;4BACT,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAC/B,MAAM,QAAQ,IAAI;4BAClB,MAAM,cAAc,MAAM,MAAM,CAAC,IAAI;4BAErC,gDAAgD;4BAChD,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,IAAI,OAAO,IAAI,KAAK,aAAa;oCAC/B,qCAAqC;oCACrC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,OAAO,mJAAU,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG;4CACvD,aAAa;4CACb,aAAa;wCACf,GAAG;oCACL;oCACA,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;gCACL,OAAO;oCACL,gCAAgC;oCAChC,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;oCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,SAAS;wCACX,GAAG;oCACL;gCACF;4BACF;4BAEA,MAAM,MAAM;wBACd;wBACA,UAAU;4BACR,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAE/B,sCAAsC;4BACtC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,OAAO,MAAM,CAAC;oCACZ,SAAS;gCACX,GAAG;gCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;oCACrB,EAAE,MAAM,CAAC;wCACP,OAAO,OAAO,KAAK;wCACnB,aAAa;wCACb,aAAa;wCACb,SAAS;oCACX,GAAG;gCACL;4BACF;4BAEA,MAAM,MAAM;wBACd;oBACF;gBACF;gBACA,gBAAgB;YAClB;YACA,QAAQ;gBACN,QAAQ;oBACN,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;YACF;QACF;QACA,QAAQ;QACR,SAAS;YACP,SAAS;QACX;QACA,WAAW;YACT,SAAS;YACT,SAAS;gBACP,eAAe;oBACb,SAAS;oBACT,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,GAAG;wBACH,QAAQ;4BACN,OAAO;gCACL,MAAM;gCACN,QAAQ;4BACV;4BACA,QAAQ;gCACN,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,WAAW;wBACT;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,GAAG,CAAC;oBACJ,GAAG;gBACL;YACF;QACF;QACA,YAAY;YACV,eAAe;gBACb,SAAS;YACX;QACF;QACA,SAAS;YACP,SAAS;YACT,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,OAAO;gBACL,OAAO;gBACP,UAAU;YACZ;YACA,SAAS;YACT,YAAY,SAAoB,UAAkB,EAAE,WAAmB,EAAE,KAAU;gBACjF,MAAM,QAAQ,IAAI,CAAC,KAAK;gBACxB,MAAM,WAAW,MAAM,QAAQ;gBAC/B,MAAM,UAAU,MAAM,OAAO;gBAC7B,MAAM,aAAa,MAAM,UAAU;gBAEnC,+BAA+B;gBAC/B,MAAM,iBAAiB,MAAM,SAAS,CAAC,aAAa;gBACpD,MAAM,gBAAgB,eAAe,qBAAqB;gBAC1D,MAAM,kBAAkB,cAAc,MAAM;gBAE5C,2DAA2D;gBAC3D,IAAI,IAAI,WAAW,aAAa,IAAI,wDAAwD;gBAC5F,IAAI,IAAI,UAAW,aAAa,IAAM,cAAc,GAAI,uBAAuB;gBAE/E,yDAAyD;gBACzD,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,gCAAgC;gBACpD,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,kBAAkB,cAAc,KAAK,+BAA+B;gBAEhG,OAAO;oBAAE;oBAAG;gBAAE;YAChB;YACA,WAAW;gBACT,MAAM,QAAQ,IAAI,CAAC,KAAK;gBACxB,MAAM,SAAS,IAAI,CAAC,MAAM;gBAC1B,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;gBAE/B,iDAAiD;gBACjD,IAAI,cAAqB,EAAE;gBAC3B,IAAI,aAAa;gBACjB,IAAI,aAAa;gBAEjB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;oBACpB,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,EAAE;wBAC1B,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;4BAChB,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,KAAK,aAAa,EAAE,CAAC,GAAG,GAAG;gCAChD,YAAY,IAAI,CAAC;oCACf,UAAU,EAAE,QAAQ;oCACpB,OAAO,EAAE,CAAC;oCACV,YAAY,EAAE,IAAI;oCAClB,OAAO,EAAE,KAAK;gCAChB;gCACA,cAAc,EAAE,CAAC;gCACjB;4BACF;wBACF;oBACF;gBACF;gBAEA,kBAAkB;gBAClB,MAAM,eAAe,aAAa,IAAI,aAAa,aAAa;gBAEhE,mDAAmD;gBACnD,IAAI,cAAc,CAAC,qFAAqF,CAAC;gBAEzG,yBAAyB;gBACzB,eAAe,CAAC,4JAA4J,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;gBAEjM,yCAAyC;gBACzC,eAAe,CAAC,+EAA+E,CAAC;gBAEhG,yCAAyC;gBACzC,YAAY,OAAO,CAAC,CAAC,MAAM;oBACzB,MAAM,iBAAiB,KAAK,QAAQ,KAAK,MAAM,QAAQ;oBACvD,MAAM,UAAU,iBAAiB,6BAA6B;oBAC9D,MAAM,YAAY,iBAAiB,YAAY;oBAC/C,MAAM,aAAa,iBAAiB,YAAY;oBAEhD,eAAe,CAAC,iHAAiH,EAAE,QAAQ,yBAAyB,EAAE,iBAAiB,YAAY,cAAc,GAAG,CAAC;oBACrN,eAAe,CAAC,wDAAwD,EAAE,KAAK,KAAK,CAAC,wFAAwF,CAAC;oBAC9K,eAAe,CAAC,8CAA8C,EAAE,UAAU,eAAe,EAAE,iBAAiB,QAAQ,MAAM,GAAG,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC;oBACrJ,eAAe,CAAC,uCAAuC,EAAE,WAAW,sCAAsC,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;oBAC1I,eAAe,CAAC,MAAM,CAAC;gBACzB;gBAEA,eAAe,CAAC,MAAM,CAAC;gBAEvB,yBAAyB;gBACzB,eAAe,CAAC,oJAAoJ,CAAC;gBACrK,eAAe,CAAC,oGAAoG,CAAC;gBACrH,eAAe,CAAC,4DAA4D,CAAC;gBAC7E,eAAe,CAAC,2HAA2H,EAAE,aAAa,OAAO,CAAC,GAAG,OAAO,CAAC;gBAC7K,eAAe,CAAC,MAAM,CAAC;gBACvB,eAAe,CAAC,MAAM,CAAC;gBAEvB,eAAe,CAAC,MAAM,CAAC;gBAEvB,OAAO;YACT;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;oBACP,IAAI,SAAS,OAAO,EAAE;wBACpB,IAAI,SAAS,OAAO,CAAC,iBAAiB,EAAE;4BACtC,SAAS,OAAO,CAAC,iBAAiB;wBACpC,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,uBAAuB,EAAE;4BAC3D,SAAS,OAAO,CAAS,uBAAuB;wBACnD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,oBAAoB,EAAE;4BACxD,SAAS,OAAO,CAAS,oBAAoB;wBAChD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,mBAAmB,EAAE;4BACvD,SAAS,OAAO,CAAS,mBAAmB;wBAC/C;oBACF;gBACF;gBACA,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,OAAM;8BAEN,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAIR,8OAAC;gBAAI,KAAK;gBAAU,WAAU;0BAC5B,cAAA,8OAAC,gMAAe;oBACd,YAAY,mJAAU;oBACtB,SAAS;;;;;;;;;;;;;;;;;AAKnB;uCAEe", "debugId": null}}]}