'use client';

import React, { useState } from 'react';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';

interface ColumnChartProps {
  title?: string;
  data?: Array<{ name: string; y: number }>;
}

interface SelectedDataPoint {
  name: string;
  value: number;
  index: number;
}

// Információs panel komponens
const DataInfoPanel: React.FC<{ selectedData: SelectedDataPoint | null; allData: Array<{ name: string; y: number }> }> = ({ selectedData, allData }) => {
  if (!selectedData) {
    return (
      <div className="w-80 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ml-4 transition-all duration-300">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-2">Adatok</h3>
          <p className="text-gray-400 text-sm">
            Kattints egy oszlopra az adatok megtekintéséhez
          </p>
        </div>
      </div>
    );
  }

  // Statisztikák számítása
  const maxValue = Math.max(...allData.map(d => d.y));
  const minValue = Math.min(...allData.map(d => d.y));
  const avgValue = allData.reduce((sum, d) => sum + d.y, 0) / allData.length;
  const percentageOfMax = (selectedData.value / maxValue) * 100;
  const isAboveAverage = selectedData.value > avgValue;

  return (
    <div className="w-80 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ml-4 transition-all duration-300">
      {/* Fejléc */}
      <div className="flex items-center mb-4">
        <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center mr-3">
          <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <div>
          <h3 className="text-lg font-semibold text-white">Kiválasztott adat</h3>
          <p className="text-xs text-gray-400">#{selectedData.index + 1} pozíció</p>
        </div>
      </div>

      {/* Fő adatok */}
      <div className="space-y-4">
        <div className="bg-gray-700/50 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-300 text-sm">Kategória</span>
            <span className="text-white font-semibold">{selectedData.name}</span>
          </div>
          <div className="flex justify-between items-center">
            <span className="text-gray-300 text-sm">Érték</span>
            <span className="text-2xl font-bold text-white">{selectedData.value.toFixed(1)}</span>
          </div>
        </div>

        {/* Vizuális progress bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs text-gray-400">
            <span>Relatív érték</span>
            <span>{percentageOfMax.toFixed(1)}% a maximumból</span>
          </div>
          <div className="w-full bg-gray-700 rounded-full h-3">
            <div
              className="bg-gradient-to-r from-purple-500 to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out"
              style={{ width: `${percentageOfMax}%` }}
            ></div>
          </div>
        </div>

        {/* Statisztikák */}
        <div className="grid grid-cols-2 gap-3">
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-xs text-gray-400 mb-1">Maximum</div>
            <div className="text-sm font-semibold text-white">{maxValue.toFixed(1)}</div>
          </div>
          <div className="bg-gray-700/30 rounded-lg p-3 text-center">
            <div className="text-xs text-gray-400 mb-1">Átlag</div>
            <div className="text-sm font-semibold text-white">{avgValue.toFixed(1)}</div>
          </div>
        </div>

        {/* Összehasonlítás */}
        <div className={`rounded-lg p-3 border ${isAboveAverage ? 'bg-green-900/20 border-green-500/30' : 'bg-orange-900/20 border-orange-500/30'}`}>
          <div className="flex items-center">
            <div className={`w-2 h-2 rounded-full mr-2 ${isAboveAverage ? 'bg-green-400' : 'bg-orange-400'}`}></div>
            <span className={`text-xs ${isAboveAverage ? 'text-green-300' : 'text-orange-300'}`}>
              {isAboveAverage ? 'Átlag feletti érték' : 'Átlag alatti érték'}
            </span>
          </div>
          <div className={`text-xs mt-1 ${isAboveAverage ? 'text-green-400' : 'text-orange-400'}`}>
            {isAboveAverage ? '+' : ''}{(selectedData.value - avgValue).toFixed(1)} az átlagtól
          </div>
        </div>
      </div>
    </div>
  );
};

const ColumnChart: React.FC<ColumnChartProps> = ({
  title = "Oszlop Diagram",
  data = [
    { name: 'Január', y: 29.9 },
    { name: 'Február', y: 71.5 },
    { name: 'Március', y: 106.4 },
    { name: 'Április', y: 129.2 },
    { name: 'Május', y: 144.0 },
    { name: 'Június', y: 176.0 }
  ]
}) => {
  const chartRef = React.useRef<HTMLDivElement>(null);
  const [selectedData, setSelectedData] = useState<SelectedDataPoint | null>(null);
  const options: Highcharts.Options = {
    chart: {
      type: 'column',
      backgroundColor: 'transparent',
      style: {
        fontFamily: 'var(--font-geist-sans)'
      }
    },
    tooltip: {
      enabled: false
    },
    title: {
      text: title,
      style: {
        color: '#ffffff',
        fontSize: '20px',
        fontWeight: '600'
      }
    },
    xAxis: {
      type: 'category',
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      lineColor: '#3f3f46',
      tickColor: '#3f3f46'
    },
    yAxis: {
      title: {
        text: 'Értékek',
        style: {
          color: '#a1a1aa'
        }
      },
      labels: {
        style: {
          color: '#a1a1aa'
        }
      },
      gridLineColor: '#3f3f46'
    },
    legend: {
      enabled: false
    },
    plotOptions: {
      column: {
        borderWidth: 0,
        borderRadius: 4,
        color: {
          linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },
          stops: [
            [0, '#8b5cf6'],
            [1, '#6366f1']
          ]
        },
        dataLabels: {
          enabled: true,
          style: {
            color: '#ffffff',
            textOutline: 'none'
          }
        },
        point: {
          events: {
            click: function(this: Highcharts.Point) {
              const pointData = this as any;
              setSelectedData({
                name: pointData.name || pointData.category,
                value: pointData.y,
                index: pointData.index
              });
            }
          }
        },
        cursor: 'pointer',
        states: {
          hover: {
            brightness: 0.1
          },
          select: {
            brightness: 0.2,
            borderColor: '#ffffff',
            borderWidth: 2
          }
        }
      }
    },
    series: [{
      type: 'column',
      name: 'Adatok',
      data: data
    }],
    exporting: {
      enabled: true,
      buttons: {
        contextButton: {
          enabled: true,
          theme: {
            fill: 'rgba(55, 65, 81, 0.9)',
            stroke: '#ffffff',
            r: 4,
            states: {
              hover: {
                fill: 'rgba(75, 85, 99, 0.95)',
                stroke: '#ffffff'
              },
              select: {
                fill: 'rgba(107, 114, 128, 0.95)',
                stroke: '#ffffff'
              }
            }
          } as any,
          menuItems: [
            'viewFullscreen',
            'separator',
            'downloadPNG',
            'downloadJPEG',
            'downloadSVG'
          ],
          x: -10,
          y: 10
        }
      }
    },
    navigation: {
      buttonOptions: {
        enabled: true
      }
    },
  };

  return (
    <div className="w-full flex gap-4">
      <div className="flex-1 h-96 relative">
        <button
          onClick={() => {
            if (chartRef.current) {
              if (chartRef.current.requestFullscreen) {
                chartRef.current.requestFullscreen();
              } else if ((chartRef.current as any).webkitRequestFullscreen) {
                (chartRef.current as any).webkitRequestFullscreen();
              } else if ((chartRef.current as any).mozRequestFullScreen) {
                (chartRef.current as any).mozRequestFullScreen();
              } else if ((chartRef.current as any).msRequestFullscreen) {
                (chartRef.current as any).msRequestFullscreen();
              }
            }
          }}
          className="absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg"
          title="Teljes képernyő"
        >
          <svg
            className="w-5 h-5"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"
            />
          </svg>
        </button>
        <div ref={chartRef} className="w-full h-96">
          <HighchartsReact
            highcharts={Highcharts}
            options={options}
          />
        </div>
      </div>
      <DataInfoPanel selectedData={selectedData} allData={data} />
    </div>
  );
};

export default ColumnChart;
