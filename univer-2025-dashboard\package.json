{"name": "univer-2025-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint"}, "dependencies": {"class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "highcharts": "^12.4.0", "highcharts-react-official": "^3.2.2", "lucide-react": "^0.544.0", "next": "15.5.3", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.3.8", "typescript": "^5"}}