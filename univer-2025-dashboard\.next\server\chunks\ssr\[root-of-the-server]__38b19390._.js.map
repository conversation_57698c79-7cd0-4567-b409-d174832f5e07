{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/app/page.tsx"], "sourcesContent": ["export default function Home() {\r\n  const { isDark } = useTheme();\r\n\r\n  // Adatok feldolgozása\r\n  const erettData = processChartData('érett');\r\n  const romloData = processChartData('romló');\r\n\r\n  const erettGrouped = groupDataByBreeder(erettData);\r\n  const romloGrouped = groupDataByBreeder(romloData);\r\n\r\n  return (\r\n    <div className=\"min-h-screen p-6\">\r\n      <div className=\"max-w-[1920px] mx-auto space-y-8\">\r\n        {/* Header */}\r\n        <div className=\"text-center space-y-2\">\r\n          <h1 className=\"text-4xl font-bold\">\r\n            🍅 Univer 2025 Dashboard\r\n          </h1>\r\n          <p className=\"text-lg\">\r\n            Tövön tarthatóság elemzés nemesítőházak szerint\r\n          </p>\r\n        </div>\r\n\r\n        {/* Bal-jobb oldali elrendezés */}\r\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n          {/* Bal oldal - Érett bogyó mennyisége szekció */}\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h2 className=\"text-3xl font-bold mb-2\">\r\n                Érett bogyó mennyisége (t/ha)\r\n              </h2>\r\n              <p>\r\n                Az ép, érett bogyó mennyisége I. és II. szedés során\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              {BREEDERS.map((breeder) => {\r\n                const varieties = erettGrouped[breeder.name] || [];\r\n                if (varieties.length === 0) return null;\r\n\r\n                return (\r\n                  <Card key={`erett-${breeder.name}`} className={`w-full ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"flex items-center gap-3\">\r\n                        <div\r\n                          className=\"w-4 h-4 rounded-full\"\r\n                          style={{ backgroundColor: breeder.color }}\r\n                        />\r\n                        {breeder.name}\r\n                      </CardTitle>\r\n                      <CardDescription>\r\n                        {varieties.length} fajta adatai\r\n                      </CardDescription>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <BreederChart\r\n                        title=\"Érett bogyó mennyisége\"\r\n                        varieties={varieties}\r\n                        breederColor={breeder.color}\r\n                        breederName={breeder.name}\r\n                        allVarietiesData={erettData}\r\n                      />\r\n                    </CardContent>\r\n                  </Card>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n\r\n          {/* Jobb oldal - Romló bogyó mennyisége szekció */}\r\n          <div className=\"space-y-6\">\r\n            <div className=\"text-center\">\r\n              <h2 className=\"text-3xl font-bold mb-2\">\r\n                Romló bogyó mennyisége (t/ha)\r\n              </h2>\r\n              <p>\r\n                A romló bogyó mennyisége I. és II. szedés során\r\n              </p>\r\n            </div>\r\n\r\n            <div className=\"space-y-6\">\r\n              {BREEDERS.map((breeder) => {\r\n                const varieties = romloGrouped[breeder.name] || [];\r\n                if (varieties.length === 0) return null;\r\n\r\n                return (\r\n                  <Card key={`romlo-${breeder.name}`} className={`w-full ${isDark ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'}`}>\r\n                    <CardHeader>\r\n                      <CardTitle className=\"flex items-center gap-3\">\r\n                        <div\r\n                          className=\"w-4 h-4 rounded-full\"\r\n                          style={{ backgroundColor: breeder.color }}\r\n                        />\r\n                        {breeder.name}\r\n                      </CardTitle>\r\n                      <CardDescription>\r\n                        {varieties.length} fajta adatai\r\n                      </CardDescription>\r\n                    </CardHeader>\r\n                    <CardContent>\r\n                      <BreederChart\r\n                        title=\"Romló bogyó mennyisége\"\r\n                        varieties={varieties}\r\n                        breederColor={breeder.color}\r\n                        breederName={breeder.name}\r\n                        allVarietiesData={romloData}\r\n                      />\r\n                    </CardContent>\r\n                  </Card>\r\n                );\r\n              })}\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-12 pt-8 border-t border-gray-700 text-center\">\r\n          <p className=\"text-sm\">\r\n            🍅 Paradicsom fajtakísérlet - 2025 © Minden jog fenntartva\r\n          </p>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;AAAe,SAAS;IACtB,MAAM,EAAE,MAAM,EAAE,GAAG;IAEnB,sBAAsB;IACtB,MAAM,YAAY,iBAAiB;IACnC,MAAM,YAAY,iBAAiB;IAEnC,MAAM,eAAe,mBAAmB;IACxC,MAAM,eAAe,mBAAmB;IAExC,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqB;;;;;;sCAGnC,8OAAC;4BAAE,WAAU;sCAAU;;;;;;;;;;;;8BAMzB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDAGxC,8OAAC;sDAAE;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;wCAClD,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO;wCAEnC,qBACE,8OAAC;4CAAmC,WAAW,CAAC,OAAO,EAAE,SAAS,gCAAgC,4BAA4B;;8DAC5H,8OAAC;;sEACC,8OAAC;4DAAU,WAAU;;8EACnB,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,QAAQ,KAAK;oEAAC;;;;;;gEAEzC,QAAQ,IAAI;;;;;;;sEAEf,8OAAC;;gEACE,UAAU,MAAM;gEAAC;;;;;;;;;;;;;8DAGtB,8OAAC;8DACC,cAAA,8OAAC;wDACC,OAAM;wDACN,WAAW;wDACX,cAAc,QAAQ,KAAK;wDAC3B,aAAa,QAAQ,IAAI;wDACzB,kBAAkB;;;;;;;;;;;;2CAnBb,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;oCAwBtC;;;;;;;;;;;;sCAKJ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA0B;;;;;;sDAGxC,8OAAC;sDAAE;;;;;;;;;;;;8CAKL,8OAAC;oCAAI,WAAU;8CACZ,SAAS,GAAG,CAAC,CAAC;wCACb,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;wCAClD,IAAI,UAAU,MAAM,KAAK,GAAG,OAAO;wCAEnC,qBACE,8OAAC;4CAAmC,WAAW,CAAC,OAAO,EAAE,SAAS,gCAAgC,4BAA4B;;8DAC5H,8OAAC;;sEACC,8OAAC;4DAAU,WAAU;;8EACnB,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,QAAQ,KAAK;oEAAC;;;;;;gEAEzC,QAAQ,IAAI;;;;;;;sEAEf,8OAAC;;gEACE,UAAU,MAAM;gEAAC;;;;;;;;;;;;;8DAGtB,8OAAC;8DACC,cAAA,8OAAC;wDACC,OAAM;wDACN,WAAW;wDACX,cAAc,QAAQ,KAAK;wDAC3B,aAAa,QAAQ,IAAI;wDACzB,kBAAkB;;;;;;;;;;;;2CAnBb,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;oCAwBtC;;;;;;;;;;;;;;;;;;8BAMN,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAE,WAAU;kCAAU;;;;;;;;;;;;;;;;;;;;;;AAOjC", "debugId": null}}]}