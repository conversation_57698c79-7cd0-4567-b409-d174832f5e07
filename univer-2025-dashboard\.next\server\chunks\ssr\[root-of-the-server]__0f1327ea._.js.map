{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/BreederChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect } from 'react';\r\nimport Highcharts from 'highcharts';\r\nimport HighchartsReact from 'highcharts-react-official';\r\nimport { ProcessedData } from '@/utils/dataProcessor';\r\n\r\ninterface BreederChartProps {\r\n  title: string;\r\n  varieties: ProcessedData[];\r\n  breederColor: string;\r\n  breederName: string;\r\n  allVarietiesData?: ProcessedData[]; // Az összes fajta adatai a tooltip-hez\r\n}\r\n\r\nconst BreederChart: React.FC<BreederChartProps> = ({\r\n  title,\r\n  varieties,\r\n  breederColor,\r\n  breederName,\r\n  allVarietiesData = []\r\n}) => {\r\n  const chartRef = React.useRef<HTMLDivElement>(null);\r\n\r\n  // Modulok betöltése komponens betöltéskor (egyszerűsített)\r\n  useEffect(() => {\r\n    if (typeof window !== 'undefined') {\r\n      // Egyszerűen beállítjuk a Highcharts alapbeállításokat\r\n      // Az export funkciók automatikusan működnek\r\n      console.log('Highcharts initialized with export support');\r\n    }\r\n  }, []);\r\n  // Színárnyalatok generálása a fajtákhoz\r\n  const generateColorShades = (baseColor: string, count: number): string[] => {\r\n    const colors: string[] = [];\r\n    for (let i = 0; i < count; i++) {\r\n      // Ha WALLER fajtáról van szó, akkor zöld színt használunk\r\n      if (varieties[i]?.variety === 'WALLER') {\r\n        colors.push('#16a34a'); // Zöld szín a WALLER fajtának\r\n      } else {\r\n        const factor = 0.3 + (i * 0.7) / Math.max(count - 1, 1);\r\n        colors.push(adjustColorBrightness(baseColor, factor));\r\n      }\r\n    }\r\n    return colors;\r\n  };\r\n\r\n  const adjustColorBrightness = (hex: string, factor: number): string => {\r\n    const num = parseInt(hex.replace('#', ''), 16);\r\n    const R = Math.round((num >> 16) * factor);\r\n    const G = Math.round(((num >> 8) & 0x00FF) * factor);\r\n    const B = Math.round((num & 0x0000FF) * factor);\r\n    return '#' + ((R << 16) | (G << 8) | B).toString(16).padStart(6, '0');\r\n  };\r\n\r\n  const colors = generateColorShades(breederColor, varieties.length);\r\n\r\n  // Adatok előkészítése Highcharts számára\r\n  const categories = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\r\n  \r\n  const series = varieties.map((variety, index) => ({\r\n    type: 'column' as const,\r\n    name: variety.variety,\r\n    data: categories.map(location =>\r\n      variety.locations[location as keyof typeof variety.locations]\r\n    ),\r\n    color: colors[index]\r\n  }));\r\n\r\n  const options: Highcharts.Options = {\r\n    chart: {\r\n      type: 'column',\r\n      backgroundColor: 'transparent',\r\n      style: {\r\n        fontFamily: 'var(--font-geist-sans)'\r\n      }\r\n    },\r\n    title: {\r\n      text: `${breederName}`,\r\n      style: {\r\n        color: '#ffffff',\r\n        fontSize: '18px',\r\n        fontWeight: '600'\r\n      }\r\n    },\r\n    subtitle: {\r\n      text: title,\r\n      style: {\r\n        color: '#a1a1aa',\r\n        fontSize: '14px'\r\n      }\r\n    },\r\n    xAxis: {\r\n      categories: categories,\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      lineColor: '#3f3f46',\r\n      tickColor: '#3f3f46',\r\n      crosshair: {\r\n        width: 1,\r\n        color: 'rgba(255, 255, 255, 0.3)',\r\n        dashStyle: 'Solid' as const\r\n      },\r\n      plotBands: [\r\n        {\r\n          from: -0.5,\r\n          to: 1.5,\r\n          color: 'rgba(255, 255, 255, 0.02)',\r\n          label: {\r\n            text: 'Mezőberény',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 1.5,\r\n          to: 3.5,\r\n          color: 'rgba(255, 255, 255, 0.05)',\r\n          label: {\r\n            text: 'Csabacsűd',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        },\r\n        {\r\n          from: 3.5,\r\n          to: 5.5,\r\n          color: 'rgba(255, 255, 255, 0.02)',\r\n          label: {\r\n            text: 'Lakitelek',\r\n            style: {\r\n              color: '#6b7280',\r\n              fontSize: '12px'\r\n            },\r\n            align: 'center'\r\n          }\r\n        }\r\n      ]\r\n    },\r\n    yAxis: {\r\n      title: {\r\n        text: 't/ha',\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      gridLineColor: '#3f3f46'\r\n    },\r\n    legend: {\r\n      enabled: true,\r\n      itemStyle: {\r\n        color: '#a1a1aa'\r\n      },\r\n      itemHoverStyle: {\r\n        color: '#ffffff'\r\n      }\r\n    },\r\n    plotOptions: {\r\n      column: {\r\n        borderWidth: 0,\r\n        borderRadius: 3,\r\n        groupPadding: 0.1,\r\n        pointPadding: 0.05,\r\n        dataLabels: {\r\n          enabled: false\r\n        },\r\n        states: {\r\n          hover: {\r\n            brightness: 0.2,\r\n            borderColor: '#ffffff',\r\n            borderWidth: 2\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        },\r\n        cursor: 'pointer',\r\n        point: {\r\n          events: {\r\n            mouseOver: function() {\r\n              const chart = this.series.chart;\r\n              const point = this;\r\n              const varietyName = point.series.name;\r\n\r\n              // Kiemeljük az összes ugyanolyan fajta oszlopot\r\n              chart.series.forEach((series: any) => {\r\n                if (series.name === varietyName) {\r\n                  // Kiemeljük az aktív fajta oszlopait\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      color: Highcharts.color(series.color).brighten(0.2).get(),\r\n                      borderColor: '#ffffff',\r\n                      borderWidth: 2\r\n                    }, false);\r\n                  });\r\n                  series.update({\r\n                    opacity: 1\r\n                  }, false);\r\n                } else {\r\n                  // Elhalványítjuk a többi fajtát\r\n                  series.update({\r\n                    opacity: 0.3\r\n                  }, false);\r\n                  series.points.forEach((p: any) => {\r\n                    p.update({\r\n                      opacity: 0.3\r\n                    }, false);\r\n                  });\r\n                }\r\n              });\r\n\r\n              chart.redraw();\r\n            },\r\n            mouseOut: function() {\r\n              const chart = this.series.chart;\r\n\r\n              // Visszaállítjuk az eredeti állapotot\r\n              chart.series.forEach((series: any) => {\r\n                series.update({\r\n                  opacity: 1\r\n                }, false);\r\n                series.points.forEach((p: any) => {\r\n                  p.update({\r\n                    color: series.color,\r\n                    borderColor: undefined,\r\n                    borderWidth: 0,\r\n                    opacity: 1\r\n                  }, false);\r\n                });\r\n              });\r\n\r\n              chart.redraw();\r\n            }\r\n          }\r\n        },\r\n        stickyTracking: true\r\n      },\r\n      series: {\r\n        states: {\r\n          hover: {\r\n            enabled: true\r\n          },\r\n          inactive: {\r\n            opacity: 0.3\r\n          }\r\n        },\r\n        events: {\r\n          mouseOver: function() {\r\n            const hoveredSeries = this;\r\n            const chart = hoveredSeries.chart;\r\n            \r\n            // Kiemeljük az aktuális sorozatot\r\n            chart.series.forEach((series: any) => {\r\n              if (series === hoveredSeries) {\r\n                series.points.forEach((p: any) => {\r\n                  p.update({\r\n                    color: Highcharts.color(series.color).brighten(0.2).get(),\r\n                    borderColor: '#ffffff',\r\n                    borderWidth: 2\r\n                  }, false);\r\n                });\r\n                series.setState('hover');\r\n              } else {\r\n                series.setState('inactive');\r\n              }\r\n            });\r\n            \r\n            chart.redraw();\r\n          },\r\n          mouseOut: function() {\r\n            const chart = this.chart;\r\n            \r\n            // Visszaállítjuk az eredeti állapotot\r\n            chart.series.forEach((series: any) => {\r\n              series.setState('');\r\n              series.points.forEach((p: any) => {\r\n                p.update({\r\n                  color: series.color,\r\n                  borderColor: undefined,\r\n                  borderWidth: 0\r\n                }, false);\r\n              });\r\n            });\r\n            \r\n            chart.redraw();\r\n          }\r\n        }\r\n      }\r\n    },\r\n    series: series,\r\n    credits: {\r\n      enabled: false\r\n    },\r\n    exporting: {\r\n      enabled: true,\r\n      buttons: {\r\n        contextButton: {\r\n          enabled: true,\r\n          theme: {\r\n            fill: '#374151', // Sötétszürke háttér\r\n            stroke: '#10b981', // Zöld keret hogy látszódjon\r\n            r: 4,\r\n            states: {\r\n              hover: {\r\n                fill: '#4b5563', // Világosabb szürke hover-nél\r\n                stroke: '#10b981'\r\n              },\r\n              select: {\r\n                fill: '#6b7280',\r\n                stroke: '#10b981'\r\n              }\r\n            }\r\n          } as any,\r\n          symbol: 'menu',\r\n          symbolStroke: '#ffffff', // Fehér hamburger ikon\r\n          symbolFill: '#ffffff',\r\n          symbolSize: 14,\r\n          menuItems: [\r\n            'viewFullscreen',\r\n            'separator',\r\n            'downloadPNG',\r\n            'downloadJPEG',\r\n            'downloadSVG',\r\n            'separator',\r\n            'downloadCSV',\r\n            'downloadXLS'\r\n          ],\r\n          x: -10,\r\n          y: 10\r\n        }\r\n      }\r\n    },\r\n    navigation: {\r\n      buttonOptions: {\r\n        enabled: true\r\n      }\r\n    },\r\n    tooltip: {\r\n      enabled: true,\r\n      backgroundColor: 'rgba(0, 0, 0, 0.8)',\r\n      borderColor: '#374151',\r\n      borderRadius: 8,\r\n      style: {\r\n        color: '#ffffff',\r\n        fontSize: '12px'\r\n      },\r\n      useHTML: true,\r\n      positioner: function(this: any, labelWidth: number, labelHeight: number, point: any) {\r\n        const chart = this.chart;\r\n        const plotLeft = chart.plotLeft;\r\n        const plotTop = chart.plotTop;\r\n        const plotHeight = chart.plotHeight;\r\n\r\n        // Konténer határok lekérdezése\r\n        const chartContainer = chart.container.parentElement;\r\n        const containerRect = chartContainer.getBoundingClientRect();\r\n        const containerHeight = containerRect.height;\r\n\r\n        // Tooltip a diagram bal oldalán jelenik meg - még balabbra\r\n        let x = plotLeft - labelWidth - 45; // Diagram bal szélétől 30px-re balra (nagyobb távolság)\r\n        let y = plotTop + (plotHeight / 2) - (labelHeight / 2); // Középen függőlegesen\r\n\r\n        // Biztosítjuk, hogy a tooltip ne menjen ki a konténerből\r\n        x = Math.max(5, x); // Legalább 5px-re a bal széltől\r\n        y = Math.max(5, Math.min(y, containerHeight - labelHeight - 5)); // Fentről/lentről ne lógjon ki\r\n\r\n        return { x, y };\r\n      },\r\n      formatter: function(this: any) {\r\n        const point = this.point;\r\n        const series = this.series;\r\n        const chart = this.series.chart;\r\n\r\n        // Megkeressük az összes ugyanolyan fajta adatait\r\n        let varietyData: any[] = [];\r\n        let totalValue = 0;\r\n        let validCount = 0;\r\n\r\n        chart.series.forEach((s: any) => {\r\n          if (s.name === series.name) {\r\n            s.points.forEach((p: any) => {\r\n              if (p.y !== null && p.y !== undefined && p.y > 0) {\r\n                varietyData.push({\r\n                  location: p.category,\r\n                  value: p.y,\r\n                  seriesName: s.name,\r\n                  color: s.color\r\n                });\r\n                totalValue += p.y;\r\n                validCount++;\r\n              }\r\n            });\r\n          }\r\n        });\r\n\r\n        // Átlag számítása\r\n        const averageValue = validCount > 0 ? totalValue / validCount : 0;\r\n\r\n        // Tooltip HTML összeállítása - optimális szélesség\r\n        let tooltipHtml = `<div style=\"width: 120px; max-height: 250px; display: flex; flex-direction: column;\">`;\r\n\r\n        // Fejléc - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex-shrink: 0; font-weight: bold; margin-bottom: 4px; font-size: 12px; color: #ffffff; border-bottom: 1px solid #10b981; padding-bottom: 3px;\">${series.name}</div>`;\r\n\r\n        // Scroll-ozható tartalom - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex: 1; overflow-y: auto; max-height: 150px; margin-bottom: 4px;\">`;\r\n\r\n        // Összes helyszín adatai - nagyobb betűk\r\n        varietyData.forEach((data, index) => {\r\n          const isCurrentPoint = data.location === point.category;\r\n          const bgColor = isCurrentPoint ? 'rgba(16, 185, 129, 0.15)' : 'transparent';\r\n          const textColor = isCurrentPoint ? '#10b981' : '#d1d5db';\r\n          const valueColor = isCurrentPoint ? '#ffffff' : '#9ca3af';\r\n\r\n          tooltipHtml += `<div style=\"display: flex; align-items: center; margin: 1px 0; padding: 2px 3px; border-radius: 2px; background: ${bgColor}; border-left: 1px solid ${isCurrentPoint ? '#10b981' : 'transparent'};\">`;\r\n          tooltipHtml += `<span style=\"width: 6px; height: 6px; background-color: ${data.color}; display: inline-block; margin-right: 4px; border-radius: 1px; flex-shrink: 0;\"></span>`;\r\n          tooltipHtml += `<span style=\"flex: 1; font-size: 11px; color: ${textColor}; font-weight: ${isCurrentPoint ? '600' : '400'};\">${data.location}</span>`;\r\n          tooltipHtml += `<span style=\"font-weight: bold; color: ${valueColor}; font-size: 11px; margin-left: 3px;\">${data.value.toFixed(1)}</span>`;\r\n          tooltipHtml += `</div>`;\r\n        });\r\n\r\n        tooltipHtml += `</div>`;\r\n\r\n        // Összeg - nagyobb betűk\r\n        tooltipHtml += `<div style=\"flex-shrink: 0; border-top: 1px solid rgba(255, 255, 255, 0.2); padding: 3px; background: rgba(16, 185, 129, 0.1); border-radius: 2px;\">`;\r\n        tooltipHtml += `<div style=\"display: flex; align-items: center; justify-content: space-between; font-weight: bold;\">`;\r\n        tooltipHtml += `<span style=\"font-size: 10px; color: #ffffff;\">Átlag:</span>`;\r\n        tooltipHtml += `<span style=\"font-size: 12px; color: #10b981; background: rgba(255, 255, 255, 0.1); padding: 1px 3px; border-radius: 2px;\">${averageValue.toFixed(1)}</span>`;\r\n        tooltipHtml += `</div>`;\r\n        tooltipHtml += `</div>`;\r\n\r\n        tooltipHtml += `</div>`;\r\n\r\n        return tooltipHtml;\r\n      }\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full h-96 relative\">\r\n      <button\r\n        onClick={() => {\r\n          if (chartRef.current) {\r\n            if (chartRef.current.requestFullscreen) {\r\n              chartRef.current.requestFullscreen();\r\n            } else if ((chartRef.current as any).webkitRequestFullscreen) {\r\n              (chartRef.current as any).webkitRequestFullscreen();\r\n            } else if ((chartRef.current as any).mozRequestFullScreen) {\r\n              (chartRef.current as any).mozRequestFullScreen();\r\n            } else if ((chartRef.current as any).msRequestFullscreen) {\r\n              (chartRef.current as any).msRequestFullscreen();\r\n            }\r\n          }\r\n        }}\r\n        className=\"absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg\"\r\n        title=\"Teljes képernyő\"\r\n      >\r\n        <svg\r\n          className=\"w-5 h-5\"\r\n          fill=\"none\"\r\n          stroke=\"currentColor\"\r\n          viewBox=\"0 0 24 24\"\r\n          xmlns=\"http://www.w3.org/2000/svg\"\r\n        >\r\n          <path\r\n            strokeLinecap=\"round\"\r\n            strokeLinejoin=\"round\"\r\n            strokeWidth={2}\r\n            d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\r\n          />\r\n        </svg>\r\n      </button>\r\n      <div ref={chartRef} className=\"w-full h-96\">\r\n        <HighchartsReact\r\n          highcharts={Highcharts}\r\n          options={options}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default BreederChart;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAeA,MAAM,eAA4C,CAAC,EACjD,KAAK,EACL,SAAS,EACT,YAAY,EACZ,WAAW,EACX,mBAAmB,EAAE,EACtB;IACC,MAAM,WAAW,gNAAK,CAAC,MAAM,CAAiB;IAE9C,2DAA2D;IAC3D,IAAA,kNAAS,EAAC;QACR;;IAKF,GAAG,EAAE;IACL,wCAAwC;IACxC,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,MAAM,SAAmB,EAAE;QAC3B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IAAK;YAC9B,0DAA0D;YAC1D,IAAI,SAAS,CAAC,EAAE,EAAE,YAAY,UAAU;gBACtC,OAAO,IAAI,CAAC,YAAY,8BAA8B;YACxD,OAAO;gBACL,MAAM,SAAS,MAAM,AAAC,IAAI,MAAO,KAAK,GAAG,CAAC,QAAQ,GAAG;gBACrD,OAAO,IAAI,CAAC,sBAAsB,WAAW;YAC/C;QACF;QACA,OAAO;IACT;IAEA,MAAM,wBAAwB,CAAC,KAAa;QAC1C,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;QAC3C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,OAAO,EAAE,IAAI;QACnC,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,AAAC,OAAO,IAAK,MAAM,IAAI;QAC7C,MAAM,IAAI,KAAK,KAAK,CAAC,CAAC,MAAM,QAAQ,IAAI;QACxC,OAAO,MAAM,CAAC,AAAC,KAAK,KAAO,KAAK,IAAK,CAAC,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;IACnE;IAEA,MAAM,SAAS,oBAAoB,cAAc,UAAU,MAAM;IAEjE,yCAAyC;IACzC,MAAM,aAAa;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAElE,MAAM,SAAS,UAAU,GAAG,CAAC,CAAC,SAAS,QAAU,CAAC;YAChD,MAAM;YACN,MAAM,QAAQ,OAAO;YACrB,MAAM,WAAW,GAAG,CAAC,CAAA,WACnB,QAAQ,SAAS,CAAC,SAA2C;YAE/D,OAAO,MAAM,CAAC,MAAM;QACtB,CAAC;IAED,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,OAAO;gBACL,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM,GAAG,aAAa;YACtB,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF;QACA,UAAU;YACR,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,UAAU;YACZ;QACF;QACA,OAAO;YACL,YAAY;YACZ,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,WAAW;YACX,WAAW;YACX,WAAW;gBACT,OAAO;gBACP,OAAO;gBACP,WAAW;YACb;YACA,WAAW;gBACT;oBACE,MAAM,CAAC;oBACP,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;gBACA;oBACE,MAAM;oBACN,IAAI;oBACJ,OAAO;oBACP,OAAO;wBACL,MAAM;wBACN,OAAO;4BACL,OAAO;4BACP,UAAU;wBACZ;wBACA,OAAO;oBACT;gBACF;aACD;QACH;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,eAAe;QACjB;QACA,QAAQ;YACN,SAAS;YACT,WAAW;gBACT,OAAO;YACT;YACA,gBAAgB;gBACd,OAAO;YACT;QACF;QACA,aAAa;YACX,QAAQ;gBACN,aAAa;gBACb,cAAc;gBACd,cAAc;gBACd,cAAc;gBACd,YAAY;oBACV,SAAS;gBACX;gBACA,QAAQ;oBACN,OAAO;wBACL,YAAY;wBACZ,aAAa;wBACb,aAAa;oBACf;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;gBACA,QAAQ;gBACR,OAAO;oBACL,QAAQ;wBACN,WAAW;4BACT,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAC/B,MAAM,QAAQ,IAAI;4BAClB,MAAM,cAAc,MAAM,MAAM,CAAC,IAAI;4BAErC,gDAAgD;4BAChD,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,IAAI,OAAO,IAAI,KAAK,aAAa;oCAC/B,qCAAqC;oCACrC,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,OAAO,mJAAU,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG;4CACvD,aAAa;4CACb,aAAa;wCACf,GAAG;oCACL;oCACA,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;gCACL,OAAO;oCACL,gCAAgC;oCAChC,OAAO,MAAM,CAAC;wCACZ,SAAS;oCACX,GAAG;oCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;wCACrB,EAAE,MAAM,CAAC;4CACP,SAAS;wCACX,GAAG;oCACL;gCACF;4BACF;4BAEA,MAAM,MAAM;wBACd;wBACA,UAAU;4BACR,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;4BAE/B,sCAAsC;4BACtC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;gCACpB,OAAO,MAAM,CAAC;oCACZ,SAAS;gCACX,GAAG;gCACH,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;oCACrB,EAAE,MAAM,CAAC;wCACP,OAAO,OAAO,KAAK;wCACnB,aAAa;wCACb,aAAa;wCACb,SAAS;oCACX,GAAG;gCACL;4BACF;4BAEA,MAAM,MAAM;wBACd;oBACF;gBACF;gBACA,gBAAgB;YAClB;YACA,QAAQ;gBACN,QAAQ;oBACN,OAAO;wBACL,SAAS;oBACX;oBACA,UAAU;wBACR,SAAS;oBACX;gBACF;gBACA,QAAQ;oBACN,WAAW;wBACT,MAAM,gBAAgB,IAAI;wBAC1B,MAAM,QAAQ,cAAc,KAAK;wBAEjC,kCAAkC;wBAClC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;4BACpB,IAAI,WAAW,eAAe;gCAC5B,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;oCACrB,EAAE,MAAM,CAAC;wCACP,OAAO,mJAAU,CAAC,KAAK,CAAC,OAAO,KAAK,EAAE,QAAQ,CAAC,KAAK,GAAG;wCACvD,aAAa;wCACb,aAAa;oCACf,GAAG;gCACL;gCACA,OAAO,QAAQ,CAAC;4BAClB,OAAO;gCACL,OAAO,QAAQ,CAAC;4BAClB;wBACF;wBAEA,MAAM,MAAM;oBACd;oBACA,UAAU;wBACR,MAAM,QAAQ,IAAI,CAAC,KAAK;wBAExB,sCAAsC;wBACtC,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;4BACpB,OAAO,QAAQ,CAAC;4BAChB,OAAO,MAAM,CAAC,OAAO,CAAC,CAAC;gCACrB,EAAE,MAAM,CAAC;oCACP,OAAO,OAAO,KAAK;oCACnB,aAAa;oCACb,aAAa;gCACf,GAAG;4BACL;wBACF;wBAEA,MAAM,MAAM;oBACd;gBACF;YACF;QACF;QACA,QAAQ;QACR,SAAS;YACP,SAAS;QACX;QACA,WAAW;YACT,SAAS;YACT,SAAS;gBACP,eAAe;oBACb,SAAS;oBACT,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,GAAG;wBACH,QAAQ;4BACN,OAAO;gCACL,MAAM;gCACN,QAAQ;4BACV;4BACA,QAAQ;gCACN,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,QAAQ;oBACR,cAAc;oBACd,YAAY;oBACZ,YAAY;oBACZ,WAAW;wBACT;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,GAAG,CAAC;oBACJ,GAAG;gBACL;YACF;QACF;QACA,YAAY;YACV,eAAe;gBACb,SAAS;YACX;QACF;QACA,SAAS;YACP,SAAS;YACT,iBAAiB;YACjB,aAAa;YACb,cAAc;YACd,OAAO;gBACL,OAAO;gBACP,UAAU;YACZ;YACA,SAAS;YACT,YAAY,SAAoB,UAAkB,EAAE,WAAmB,EAAE,KAAU;gBACjF,MAAM,QAAQ,IAAI,CAAC,KAAK;gBACxB,MAAM,WAAW,MAAM,QAAQ;gBAC/B,MAAM,UAAU,MAAM,OAAO;gBAC7B,MAAM,aAAa,MAAM,UAAU;gBAEnC,+BAA+B;gBAC/B,MAAM,iBAAiB,MAAM,SAAS,CAAC,aAAa;gBACpD,MAAM,gBAAgB,eAAe,qBAAqB;gBAC1D,MAAM,kBAAkB,cAAc,MAAM;gBAE5C,2DAA2D;gBAC3D,IAAI,IAAI,WAAW,aAAa,IAAI,wDAAwD;gBAC5F,IAAI,IAAI,UAAW,aAAa,IAAM,cAAc,GAAI,uBAAuB;gBAE/E,yDAAyD;gBACzD,IAAI,KAAK,GAAG,CAAC,GAAG,IAAI,gCAAgC;gBACpD,IAAI,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,kBAAkB,cAAc,KAAK,+BAA+B;gBAEhG,OAAO;oBAAE;oBAAG;gBAAE;YAChB;YACA,WAAW;gBACT,MAAM,QAAQ,IAAI,CAAC,KAAK;gBACxB,MAAM,SAAS,IAAI,CAAC,MAAM;gBAC1B,MAAM,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;gBAE/B,iDAAiD;gBACjD,IAAI,cAAqB,EAAE;gBAC3B,IAAI,aAAa;gBACjB,IAAI,aAAa;gBAEjB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAC;oBACpB,IAAI,EAAE,IAAI,KAAK,OAAO,IAAI,EAAE;wBAC1B,EAAE,MAAM,CAAC,OAAO,CAAC,CAAC;4BAChB,IAAI,EAAE,CAAC,KAAK,QAAQ,EAAE,CAAC,KAAK,aAAa,EAAE,CAAC,GAAG,GAAG;gCAChD,YAAY,IAAI,CAAC;oCACf,UAAU,EAAE,QAAQ;oCACpB,OAAO,EAAE,CAAC;oCACV,YAAY,EAAE,IAAI;oCAClB,OAAO,EAAE,KAAK;gCAChB;gCACA,cAAc,EAAE,CAAC;gCACjB;4BACF;wBACF;oBACF;gBACF;gBAEA,kBAAkB;gBAClB,MAAM,eAAe,aAAa,IAAI,aAAa,aAAa;gBAEhE,mDAAmD;gBACnD,IAAI,cAAc,CAAC,qFAAqF,CAAC;gBAEzG,yBAAyB;gBACzB,eAAe,CAAC,4JAA4J,EAAE,OAAO,IAAI,CAAC,MAAM,CAAC;gBAEjM,yCAAyC;gBACzC,eAAe,CAAC,+EAA+E,CAAC;gBAEhG,yCAAyC;gBACzC,YAAY,OAAO,CAAC,CAAC,MAAM;oBACzB,MAAM,iBAAiB,KAAK,QAAQ,KAAK,MAAM,QAAQ;oBACvD,MAAM,UAAU,iBAAiB,6BAA6B;oBAC9D,MAAM,YAAY,iBAAiB,YAAY;oBAC/C,MAAM,aAAa,iBAAiB,YAAY;oBAEhD,eAAe,CAAC,iHAAiH,EAAE,QAAQ,yBAAyB,EAAE,iBAAiB,YAAY,cAAc,GAAG,CAAC;oBACrN,eAAe,CAAC,wDAAwD,EAAE,KAAK,KAAK,CAAC,wFAAwF,CAAC;oBAC9K,eAAe,CAAC,8CAA8C,EAAE,UAAU,eAAe,EAAE,iBAAiB,QAAQ,MAAM,GAAG,EAAE,KAAK,QAAQ,CAAC,OAAO,CAAC;oBACrJ,eAAe,CAAC,uCAAuC,EAAE,WAAW,sCAAsC,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG,OAAO,CAAC;oBAC1I,eAAe,CAAC,MAAM,CAAC;gBACzB;gBAEA,eAAe,CAAC,MAAM,CAAC;gBAEvB,yBAAyB;gBACzB,eAAe,CAAC,oJAAoJ,CAAC;gBACrK,eAAe,CAAC,oGAAoG,CAAC;gBACrH,eAAe,CAAC,4DAA4D,CAAC;gBAC7E,eAAe,CAAC,2HAA2H,EAAE,aAAa,OAAO,CAAC,GAAG,OAAO,CAAC;gBAC7K,eAAe,CAAC,MAAM,CAAC;gBACvB,eAAe,CAAC,MAAM,CAAC;gBAEvB,eAAe,CAAC,MAAM,CAAC;gBAEvB,OAAO;YACT;QACF;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,SAAS;oBACP,IAAI,SAAS,OAAO,EAAE;wBACpB,IAAI,SAAS,OAAO,CAAC,iBAAiB,EAAE;4BACtC,SAAS,OAAO,CAAC,iBAAiB;wBACpC,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,uBAAuB,EAAE;4BAC3D,SAAS,OAAO,CAAS,uBAAuB;wBACnD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,oBAAoB,EAAE;4BACxD,SAAS,OAAO,CAAS,oBAAoB;wBAChD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,mBAAmB,EAAE;4BACvD,SAAS,OAAO,CAAS,mBAAmB;wBAC/C;oBACF;gBACF;gBACA,WAAU;gBACV,OAAM;0BAEN,cAAA,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;oBACR,OAAM;8BAEN,cAAA,8OAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;0BAIR,8OAAC;gBAAI,KAAK;gBAAU,WAAU;0BAC5B,cAAA,8OAAC,gMAAe;oBACd,YAAY,mJAAU;oBACtB,SAAS;;;;;;;;;;;;;;;;;AAKnB;uCAEe", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,sKAAO,EAAC,IAAA,6IAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EACX,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,IAAA,yHAAE,EAAC,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/InsightsPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card } from '@/components/ui/card';\nimport { \n  TrendingUp, \n  TrendingDown, \n  AlertCircle, \n  Info,\n  ChevronRight,\n  X,\n  Bell,\n  BellOff\n} from 'lucide-react';\nimport { ProcessedData } from '@/utils/dataProcessor';\n\ninterface InsightsPanelProps {\n  data: ProcessedData[];\n  breederName: string;\n}\n\ninterface Insight {\n  id: string;\n  type: 'trend' | 'anomaly' | 'summary' | 'alert';\n  severity: 'info' | 'warning' | 'success' | 'error';\n  title: string;\n  description: string;\n  value?: string;\n  change?: number;\n  timestamp: Date;\n}\n\nconst InsightsPanel: React.FC<InsightsPanelProps> = ({ data, breederName }) => {\n  const [insights, setInsights] = useState<Insight[]>([]);\n  const [showNotifications, setShowNotifications] = useState(true);\n  const [dismissedInsights, setDismissedInsights] = useState<string[]>([]);\n\n  // Analyze data and generate insights\n  useEffect(() => {\n    const generateInsights = () => {\n      const newInsights: Insight[] = [];\n\n      // Calculate averages and trends\n      data.forEach(variety => {\n        const values = Object.values(variety.locations).filter(v => v > 0);\n        const avg = values.reduce((a, b) => a + b, 0) / values.length;\n        const max = Math.max(...values);\n        const min = Math.min(...values);\n\n        // Trend detection\n        const firstHalf = values.slice(0, Math.floor(values.length / 2));\n        const secondHalf = values.slice(Math.floor(values.length / 2));\n        const firstAvg = firstHalf.reduce((a, b) => a + b, 0) / firstHalf.length;\n        const secondAvg = secondHalf.reduce((a, b) => a + b, 0) / secondHalf.length;\n        const trend = ((secondAvg - firstAvg) / firstAvg) * 100;\n\n        // Generate trend insight\n        if (Math.abs(trend) > 10) {\n          newInsights.push({\n            id: `trend-${variety.variety}`,\n            type: 'trend',\n            severity: trend > 0 ? 'success' : 'warning',\n            title: `${variety.variety} ${trend > 0 ? 'növekvő' : 'csökkenő'} trend`,\n            description: `${Math.abs(trend).toFixed(1)}%-os ${trend > 0 ? 'növekedés' : 'csökkenés'} tapasztalható`,\n            value: `${avg.toFixed(1)} t/ha`,\n            change: trend,\n            timestamp: new Date()\n          });\n        }\n\n        // Anomaly detection\n        const threshold = avg * 0.3; // 30% deviation\n        values.forEach((value, index) => {\n          if (Math.abs(value - avg) > threshold) {\n            const locationNames = Object.keys(variety.locations);\n            newInsights.push({\n              id: `anomaly-${variety.variety}-${index}`,\n              type: 'anomaly',\n              severity: 'warning',\n              title: `Kiugró érték: ${variety.variety}`,\n              description: `${locationNames[index]} helyszínen: ${value.toFixed(1)} t/ha (átlag: ${avg.toFixed(1)} t/ha)`,\n              timestamp: new Date()\n            });\n          }\n        });\n\n        // Best performer\n        if (avg > 30) {\n          newInsights.push({\n            id: `best-${variety.variety}`,\n            type: 'summary',\n            severity: 'success',\n            title: `Kiváló teljesítmény: ${variety.variety}`,\n            description: `Átlag ${avg.toFixed(1)} t/ha - Top performer`,\n            value: `${max.toFixed(1)} t/ha max`,\n            timestamp: new Date()\n          });\n        }\n      });\n\n      // Overall summary\n      const overallAvg = data.reduce((sum, variety) => {\n        const values = Object.values(variety.locations).filter(v => v > 0);\n        const avg = values.reduce((a, b) => a + b, 0) / values.length;\n        return sum + avg;\n      }, 0) / data.length;\n\n      newInsights.push({\n        id: 'overall-summary',\n        type: 'summary',\n        severity: 'info',\n        title: `${breederName} összesítés`,\n        description: `Átlagos termés: ${overallAvg.toFixed(1)} t/ha`,\n        value: `${data.length} fajta`,\n        timestamp: new Date()\n      });\n\n      setInsights(newInsights);\n    };\n\n    if (data && data.length > 0) {\n      generateInsights();\n    }\n  }, [data, breederName]);\n\n  // Filter out dismissed insights\n  const visibleInsights = insights.filter(i => !dismissedInsights.includes(i.id));\n\n  const dismissInsight = (id: string) => {\n    setDismissedInsights(prev => [...prev, id]);\n  };\n\n  const getIcon = (type: Insight['type'], severity: Insight['severity']) => {\n    if (type === 'trend') {\n      return severity === 'success' ? \n        <TrendingUp className=\"w-5 h-5 text-green-500\" /> : \n        <TrendingDown className=\"w-5 h-5 text-red-500\" />;\n    }\n    if (type === 'anomaly') {\n      return <AlertCircle className=\"w-5 h-5 text-yellow-500\" />;\n    }\n    return <Info className=\"w-5 h-5 text-blue-500\" />;\n  };\n\n  const getSeverityColor = (severity: Insight['severity']) => {\n    switch (severity) {\n      case 'success': return 'border-green-500 bg-green-500/10';\n      case 'warning': return 'border-yellow-500 bg-yellow-500/10';\n      case 'error': return 'border-red-500 bg-red-500/10';\n      default: return 'border-blue-500 bg-blue-500/10';\n    }\n  };\n\n  if (!showNotifications || visibleInsights.length === 0) {\n    return (\n      <button\n        onClick={() => setShowNotifications(true)}\n        className=\"fixed bottom-4 right-4 p-3 bg-green-600 hover:bg-green-700 rounded-full shadow-lg transition-colors z-50\"\n        title=\"Show insights\"\n      >\n        <Bell className=\"w-6 h-6 text-white\" />\n        {visibleInsights.length > 0 && (\n          <span className=\"absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center\">\n            {visibleInsights.length}\n          </span>\n        )}\n      </button>\n    );\n  }\n\n  return (\n    <div className=\"fixed bottom-4 right-4 w-96 max-h-[600px] overflow-y-auto z-50\">\n      {/* Header */}\n      <div className=\"bg-gray-800 p-3 rounded-t-lg flex justify-between items-center sticky top-0 z-10\">\n        <div className=\"flex items-center gap-2\">\n          <Bell className=\"w-5 h-5 text-green-500\" />\n          <h3 className=\"text-white font-semibold\">Értesítések & Insights</h3>\n          <span className=\"bg-green-600 text-white text-xs px-2 py-1 rounded-full\">\n            {visibleInsights.length}\n          </span>\n        </div>\n        <button\n          onClick={() => setShowNotifications(false)}\n          className=\"p-1 hover:bg-gray-700 rounded transition-colors\"\n        >\n          <BellOff className=\"w-4 h-4 text-gray-400\" />\n        </button>\n      </div>\n\n      {/* Insights List */}\n      <div className=\"bg-gray-900 rounded-b-lg shadow-2xl\">\n        {visibleInsights.map((insight, index) => (\n          <Card\n            key={insight.id}\n            className={`\n              m-2 p-3 border-l-4 ${getSeverityColor(insight.severity)}\n              hover:bg-gray-800 transition-all cursor-pointer\n              ${index === 0 ? 'animate-pulse' : ''}\n            `}\n          >\n            <div className=\"flex items-start justify-between\">\n              <div className=\"flex items-start gap-3 flex-1\">\n                {getIcon(insight.type, insight.severity)}\n                <div className=\"flex-1\">\n                  <h4 className=\"text-white font-medium text-sm\">{insight.title}</h4>\n                  <p className=\"text-gray-400 text-xs mt-1\">{insight.description}</p>\n                  {insight.value && (\n                    <div className=\"flex items-center gap-2 mt-2\">\n                      <span className=\"text-green-500 font-bold text-sm\">{insight.value}</span>\n                      {insight.change && (\n                        <span className={`text-xs ${insight.change > 0 ? 'text-green-400' : 'text-red-400'}`}>\n                          {insight.change > 0 ? '+' : ''}{insight.change.toFixed(1)}%\n                        </span>\n                      )}\n                    </div>\n                  )}\n                  <p className=\"text-gray-500 text-xs mt-2\">\n                    {new Date(insight.timestamp).toLocaleTimeString('hu-HU')}\n                  </p>\n                </div>\n              </div>\n              <button\n                onClick={() => dismissInsight(insight.id)}\n                className=\"p-1 hover:bg-gray-700 rounded transition-colors\"\n                title=\"Dismiss\"\n              >\n                <X className=\"w-4 h-4 text-gray-400\" />\n              </button>\n            </div>\n          </Card>\n        ))}\n      </div>\n\n      {/* View All Button */}\n      <button className=\"w-full bg-gray-800 hover:bg-gray-700 text-white py-2 rounded-lg mt-2 flex items-center justify-center gap-2 transition-colors\">\n        <span className=\"text-sm\">Összes megtekintése</span>\n        <ChevronRight className=\"w-4 h-4\" />\n      </button>\n    </div>\n  );\n};\n\nexport default InsightsPanel;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AAgCA,MAAM,gBAA8C,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE;IACxE,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAAC;IAC3D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,IAAA,iNAAQ,EAAW,EAAE;IAEvE,qCAAqC;IACrC,IAAA,kNAAS,EAAC;QACR,MAAM,mBAAmB;YACvB,MAAM,cAAyB,EAAE;YAEjC,gCAAgC;YAChC,KAAK,OAAO,CAAC,CAAA;gBACX,MAAM,SAAS,OAAO,MAAM,CAAC,QAAQ,SAAS,EAAE,MAAM,CAAC,CAAA,IAAK,IAAI;gBAChE,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,OAAO,MAAM;gBAC7D,MAAM,MAAM,KAAK,GAAG,IAAI;gBACxB,MAAM,MAAM,KAAK,GAAG,IAAI;gBAExB,kBAAkB;gBAClB,MAAM,YAAY,OAAO,KAAK,CAAC,GAAG,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG;gBAC7D,MAAM,aAAa,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,OAAO,MAAM,GAAG;gBAC3D,MAAM,WAAW,UAAU,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,UAAU,MAAM;gBACxE,MAAM,YAAY,WAAW,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,WAAW,MAAM;gBAC3E,MAAM,QAAQ,AAAC,CAAC,YAAY,QAAQ,IAAI,WAAY;gBAEpD,yBAAyB;gBACzB,IAAI,KAAK,GAAG,CAAC,SAAS,IAAI;oBACxB,YAAY,IAAI,CAAC;wBACf,IAAI,CAAC,MAAM,EAAE,QAAQ,OAAO,EAAE;wBAC9B,MAAM;wBACN,UAAU,QAAQ,IAAI,YAAY;wBAClC,OAAO,GAAG,QAAQ,OAAO,CAAC,CAAC,EAAE,QAAQ,IAAI,YAAY,WAAW,MAAM,CAAC;wBACvE,aAAa,GAAG,KAAK,GAAG,CAAC,OAAO,OAAO,CAAC,GAAG,KAAK,EAAE,QAAQ,IAAI,cAAc,YAAY,cAAc,CAAC;wBACvG,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;wBAC/B,QAAQ;wBACR,WAAW,IAAI;oBACjB;gBACF;gBAEA,oBAAoB;gBACpB,MAAM,YAAY,MAAM,KAAK,gBAAgB;gBAC7C,OAAO,OAAO,CAAC,CAAC,OAAO;oBACrB,IAAI,KAAK,GAAG,CAAC,QAAQ,OAAO,WAAW;wBACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,QAAQ,SAAS;wBACnD,YAAY,IAAI,CAAC;4BACf,IAAI,CAAC,QAAQ,EAAE,QAAQ,OAAO,CAAC,CAAC,EAAE,OAAO;4BACzC,MAAM;4BACN,UAAU;4BACV,OAAO,CAAC,cAAc,EAAE,QAAQ,OAAO,EAAE;4BACzC,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,aAAa,EAAE,MAAM,OAAO,CAAC,GAAG,cAAc,EAAE,IAAI,OAAO,CAAC,GAAG,MAAM,CAAC;4BAC3G,WAAW,IAAI;wBACjB;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,IAAI,MAAM,IAAI;oBACZ,YAAY,IAAI,CAAC;wBACf,IAAI,CAAC,KAAK,EAAE,QAAQ,OAAO,EAAE;wBAC7B,MAAM;wBACN,UAAU;wBACV,OAAO,CAAC,qBAAqB,EAAE,QAAQ,OAAO,EAAE;wBAChD,aAAa,CAAC,MAAM,EAAE,IAAI,OAAO,CAAC,GAAG,qBAAqB,CAAC;wBAC3D,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,SAAS,CAAC;wBACnC,WAAW,IAAI;oBACjB;gBACF;YACF;YAEA,kBAAkB;YAClB,MAAM,aAAa,KAAK,MAAM,CAAC,CAAC,KAAK;gBACnC,MAAM,SAAS,OAAO,MAAM,CAAC,QAAQ,SAAS,EAAE,MAAM,CAAC,CAAA,IAAK,IAAI;gBAChE,MAAM,MAAM,OAAO,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,GAAG,KAAK,OAAO,MAAM;gBAC7D,OAAO,MAAM;YACf,GAAG,KAAK,KAAK,MAAM;YAEnB,YAAY,IAAI,CAAC;gBACf,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,OAAO,GAAG,YAAY,WAAW,CAAC;gBAClC,aAAa,CAAC,gBAAgB,EAAE,WAAW,OAAO,CAAC,GAAG,KAAK,CAAC;gBAC5D,OAAO,GAAG,KAAK,MAAM,CAAC,MAAM,CAAC;gBAC7B,WAAW,IAAI;YACjB;YAEA,YAAY;QACd;QAEA,IAAI,QAAQ,KAAK,MAAM,GAAG,GAAG;YAC3B;QACF;IACF,GAAG;QAAC;QAAM;KAAY;IAEtB,gCAAgC;IAChC,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,kBAAkB,QAAQ,CAAC,EAAE,EAAE;IAE7E,MAAM,iBAAiB,CAAC;QACtB,qBAAqB,CAAA,OAAQ;mBAAI;gBAAM;aAAG;IAC5C;IAEA,MAAM,UAAU,CAAC,MAAuB;QACtC,IAAI,SAAS,SAAS;YACpB,OAAO,aAAa,0BAClB,8OAAC,gOAAU;gBAAC,WAAU;;;;;yEACtB,8OAAC,sOAAY;gBAAC,WAAU;;;;;;QAC5B;QACA,IAAI,SAAS,WAAW;YACtB,qBAAO,8OAAC,mOAAW;gBAAC,WAAU;;;;;;QAChC;QACA,qBAAO,8OAAC,0MAAI;YAAC,WAAU;;;;;;IACzB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB;gBAAS,OAAO;QAClB;IACF;IAEA,IAAI,CAAC,qBAAqB,gBAAgB,MAAM,KAAK,GAAG;QACtD,qBACE,8OAAC;YACC,SAAS,IAAM,qBAAqB;YACpC,WAAU;YACV,OAAM;;8BAEN,8OAAC,0MAAI;oBAAC,WAAU;;;;;;gBACf,gBAAgB,MAAM,GAAG,mBACxB,8OAAC;oBAAK,WAAU;8BACb,gBAAgB,MAAM;;;;;;;;;;;;IAKjC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,0MAAI;gCAAC,WAAU;;;;;;0CAChB,8OAAC;gCAAG,WAAU;0CAA2B;;;;;;0CACzC,8OAAC;gCAAK,WAAU;0CACb,gBAAgB,MAAM;;;;;;;;;;;;kCAG3B,8OAAC;wBACC,SAAS,IAAM,qBAAqB;wBACpC,WAAU;kCAEV,cAAA,8OAAC,uNAAO;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBAAI,WAAU;0BACZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,8OAAC,wIAAI;wBAEH,WAAW,CAAC;iCACS,EAAE,iBAAiB,QAAQ,QAAQ,EAAE;;cAExD,EAAE,UAAU,IAAI,kBAAkB,GAAG;YACvC,CAAC;kCAED,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;wCACZ,QAAQ,QAAQ,IAAI,EAAE,QAAQ,QAAQ;sDACvC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAkC,QAAQ,KAAK;;;;;;8DAC7D,8OAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;gDAC7D,QAAQ,KAAK,kBACZ,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAK,WAAU;sEAAoC,QAAQ,KAAK;;;;;;wDAChE,QAAQ,MAAM,kBACb,8OAAC;4DAAK,WAAW,CAAC,QAAQ,EAAE,QAAQ,MAAM,GAAG,IAAI,mBAAmB,gBAAgB;;gEACjF,QAAQ,MAAM,GAAG,IAAI,MAAM;gEAAI,QAAQ,MAAM,CAAC,OAAO,CAAC;gEAAG;;;;;;;;;;;;;8DAKlE,8OAAC;oDAAE,WAAU;8DACV,IAAI,KAAK,QAAQ,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;8CAItD,8OAAC;oCACC,SAAS,IAAM,eAAe,QAAQ,EAAE;oCACxC,WAAU;oCACV,OAAM;8CAEN,cAAA,8OAAC,iMAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;uBAjCZ,QAAQ,EAAE;;;;;;;;;;0BAyCrB,8OAAC;gBAAO,WAAU;;kCAChB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;kCAC1B,8OAAC,sOAAY;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAIhC;uCAEe", "debugId": null}}, {"offset": {"line": 1017, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/utils/dataProcessor.ts"], "sourcesContent": ["import rawData from '@/data/raw_excel_data.json';\n\nexport interface ChartDataPoint {\n  name: string;\n  y: number;\n  color?: string;\n}\n\nexport interface BreederGroup {\n  name: string;\n  color: string;\n  varieties: string[];\n}\n\nexport interface ProcessedData {\n  variety: string;\n  breeder: string;\n  locations: {\n    'M-I': number;\n    'M-II': number;\n    'Cs-I': number;\n    'Cs-II': number;\n    'L-I': number;\n    'L-II': number;\n  };\n}\n\n// Nemesí<PERSON><PERSON><PERSON><PERSON><PERSON> definíciója\nexport const BREEDERS: BreederGroup[] = [\n  {\n    name: 'Unigen Seeds',\n    color: '#dc2626', // Piros\n    varieties: ['UG11227*', 'UG8492', 'UG17219', 'UG1578', 'UG13577*']\n  },\n  {\n    name: 'BASF-Nunhems',\n    color: '#d97706', // Mustár narancssárga\n    varieties: ['N00541*', 'N00530', 'N00544', 'N00539', 'N00339', 'N4510', 'N00540*']\n  },\n  {\n    name: '<PERSON> + <PERSON>',\n    color: '#1e40af', // Királykék\n    varieties: ['WALLER', 'H2123*', 'H2239', 'H2249', 'H1881', 'H2127']\n  }\n];\n\n// Helyszínek csoportosítása\nexport const LOCATION_GROUPS = [\n  { name: 'Mezőberény', locations: ['M-I', 'M-II'], color: '#8b5cf6' },\n  { name: 'Csabacsűd', locations: ['Cs-I', 'Cs-II'], color: '#06b6d4' },\n  { name: 'Lakitelek', locations: ['L-I', 'L-II'], color: '#84cc16' }\n];\n\nexport function getBreederForVariety(variety: string): string {\n  for (const breeder of BREEDERS) {\n    if (breeder.varieties.includes(variety)) {\n      return breeder.name;\n    }\n  }\n  return 'Ismeretlen';\n}\n\nexport function getBreederColor(breederName: string): string {\n  const breeder = BREEDERS.find(b => b.name === breederName);\n  return breeder?.color || '#6b7280';\n}\n\nexport function processChartData(chartType: 'érett' | 'romló'): ProcessedData[] {\n  const targetDiagram = chartType === 'érett' \n    ? 'Tövön tarthatóság - az ép, érett bogyó mennyisége, I. és II. szedés, t/ha'\n    : 'Tövön tarthatóság - a romló bogyó mennyisége, I. és II. szedés, t/ha';\n\n  const filteredData = rawData.Munka1.filter(item => item.diagramhoz === targetDiagram);\n\n  return filteredData.map(item => ({\n    variety: item.fajta,\n    breeder: getBreederForVariety(item.fajta),\n    locations: {\n      'M-I': item['M-I.'],\n      'M-II': item['M-II.'],\n      'Cs-I': item['Cs-I.'],\n      'Cs-II': item['Cs-II.'],\n      'L-I': item['L-I.'],\n      'L-II': item['L-II.']\n    }\n  }));\n}\n\nexport function groupDataByBreeder(data: ProcessedData[]): Record<string, ProcessedData[]> {\n  return data.reduce((acc, item) => {\n    if (!acc[item.breeder]) {\n      acc[item.breeder] = [];\n    }\n    acc[item.breeder].push(item);\n    return acc;\n  }, {} as Record<string, ProcessedData[]>);\n}\n\nexport function createChartSeriesData(varieties: ProcessedData[], breederColor: string): any[] {\n  const locations = ['M-I', 'M-II', 'Cs-I', 'Cs-II', 'L-I', 'L-II'];\n  \n  return varieties.map((variety, varietyIndex) => ({\n    name: variety.variety,\n    data: locations.map((location, locationIndex) => ({\n      name: location,\n      y: variety.locations[location as keyof typeof variety.locations],\n      color: adjustColorBrightness(breederColor, varietyIndex * 0.2)\n    })),\n    color: adjustColorBrightness(breederColor, varietyIndex * 0.2)\n  }));\n}\n\nfunction adjustColorBrightness(hex: string, factor: number): string {\n  // Egyszerű színárnyalat módosítás\n  const num = parseInt(hex.replace('#', ''), 16);\n  const amt = Math.round(2.55 * factor * 100);\n  const R = (num >> 16) + amt;\n  const G = (num >> 8 & 0x00FF) + amt;\n  const B = (num & 0x0000FF) + amt;\n  return '#' + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +\n    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +\n    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA;;AA4BO,MAAM,WAA2B;IACtC;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAY;YAAU;YAAW;YAAU;SAAW;IACpE;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAW;YAAU;YAAU;YAAU;YAAU;YAAS;SAAU;IACpF;IACA;QACE,MAAM;QACN,OAAO;QACP,WAAW;YAAC;YAAU;YAAU;YAAS;YAAS;YAAS;SAAQ;IACrE;CACD;AAGM,MAAM,kBAAkB;IAC7B;QAAE,MAAM;QAAc,WAAW;YAAC;YAAO;SAAO;QAAE,OAAO;IAAU;IACnE;QAAE,MAAM;QAAa,WAAW;YAAC;YAAQ;SAAQ;QAAE,OAAO;IAAU;IACpE;QAAE,MAAM;QAAa,WAAW;YAAC;YAAO;SAAO;QAAE,OAAO;IAAU;CACnE;AAEM,SAAS,qBAAqB,OAAe;IAClD,KAAK,MAAM,WAAW,SAAU;QAC9B,IAAI,QAAQ,SAAS,CAAC,QAAQ,CAAC,UAAU;YACvC,OAAO,QAAQ,IAAI;QACrB;IACF;IACA,OAAO;AACT;AAEO,SAAS,gBAAgB,WAAmB;IACjD,MAAM,UAAU,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK;IAC9C,OAAO,SAAS,SAAS;AAC3B;AAEO,SAAS,iBAAiB,SAA4B;IAC3D,MAAM,gBAAgB,cAAc,UAChC,8EACA;IAEJ,MAAM,eAAe,gHAAO,CAAC,MAAM,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,UAAU,KAAK;IAEvE,OAAO,aAAa,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC/B,SAAS,KAAK,KAAK;YACnB,SAAS,qBAAqB,KAAK,KAAK;YACxC,WAAW;gBACT,OAAO,IAAI,CAAC,OAAO;gBACnB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,QAAQ,IAAI,CAAC,QAAQ;gBACrB,SAAS,IAAI,CAAC,SAAS;gBACvB,OAAO,IAAI,CAAC,OAAO;gBACnB,QAAQ,IAAI,CAAC,QAAQ;YACvB;QACF,CAAC;AACH;AAEO,SAAS,mBAAmB,IAAqB;IACtD,OAAO,KAAK,MAAM,CAAC,CAAC,KAAK;QACvB,IAAI,CAAC,GAAG,CAAC,KAAK,OAAO,CAAC,EAAE;YACtB,GAAG,CAAC,KAAK,OAAO,CAAC,GAAG,EAAE;QACxB;QACA,GAAG,CAAC,KAAK,OAAO,CAAC,CAAC,IAAI,CAAC;QACvB,OAAO;IACT,GAAG,CAAC;AACN;AAEO,SAAS,sBAAsB,SAA0B,EAAE,YAAoB;IACpF,MAAM,YAAY;QAAC;QAAO;QAAQ;QAAQ;QAAS;QAAO;KAAO;IAEjE,OAAO,UAAU,GAAG,CAAC,CAAC,SAAS,eAAiB,CAAC;YAC/C,MAAM,QAAQ,OAAO;YACrB,MAAM,UAAU,GAAG,CAAC,CAAC,UAAU,gBAAkB,CAAC;oBAChD,MAAM;oBACN,GAAG,QAAQ,SAAS,CAAC,SAA2C;oBAChE,OAAO,sBAAsB,cAAc,eAAe;gBAC5D,CAAC;YACD,OAAO,sBAAsB,cAAc,eAAe;QAC5D,CAAC;AACH;AAEA,SAAS,sBAAsB,GAAW,EAAE,MAAc;IACxD,kCAAkC;IAClC,MAAM,MAAM,SAAS,IAAI,OAAO,CAAC,KAAK,KAAK;IAC3C,MAAM,MAAM,KAAK,KAAK,CAAC,OAAO,SAAS;IACvC,MAAM,IAAI,CAAC,OAAO,EAAE,IAAI;IACxB,MAAM,IAAI,CAAC,OAAO,IAAI,MAAM,IAAI;IAChC,MAAM,IAAI,CAAC,MAAM,QAAQ,IAAI;IAC7B,OAAO,MAAM,CAAC,YAAY,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,UAC1D,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,IAAI,QAClC,CAAC,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,GAAG,CAAC,EAAE,QAAQ,CAAC,IAAI,KAAK,CAAC;AACxD", "debugId": null}}, {"offset": {"line": 1168, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from \"react\";\nimport Breeder<PERSON>hart from \"@/components/BreederChart\";\nimport DashboardGrid from \"@/components/DashboardGrid\";\nimport InsightsPanel from \"@/components/InsightsPanel\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { processChartData, groupDataByBreeder, BREEDERS, getBreederColor } from \"@/utils/dataProcessor\";\n\nexport default function Home() {\n  // Adatok feldolgozása\n  const erettData = processChartData('érett');\n  const romloData = processChartData('romló');\n\n  const erettGrouped = groupDataByBreeder(erettData);\n  const romloGrouped = groupDataByBreeder(romloData);\n\n  // State for insights panel\n  const [selectedBreeder, setSelectedBreeder] = useState<string | null>(null);\n\n  // Create chart components array for DashboardGrid\n  const chartComponents: React.ReactNode[] = [];\n\n  // Add érett charts\n  BREEDERS.forEach((breeder) => {\n    const varieties = erettGrouped[breeder.name] || [];\n    if (varieties.length > 0) {\n      chartComponents.push(\n        <Card key={`erett-${breeder.name}`} className=\"w-full\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <div\n                className=\"w-4 h-4 rounded-full\"\n                style={{ backgroundColor: breeder.color }}\n              />\n              {breeder.name} - Érett bogyó\n            </CardTitle>\n            <CardDescription>\n              {varieties.length} fajta adatai\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div \n              onMouseEnter={() => setSelectedBreeder(breeder.name)}\n              onMouseLeave={() => setSelectedBreeder(null)}\n            >\n              <BreederChart\n                title=\"Érett bogyó mennyisége\"\n                varieties={varieties}\n                breederColor={breeder.color}\n                breederName={breeder.name}\n                allVarietiesData={erettData}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      );\n    }\n  });\n\n  // Add romló charts\n  BREEDERS.forEach((breeder) => {\n    const varieties = romloGrouped[breeder.name] || [];\n    if (varieties.length > 0) {\n      chartComponents.push(\n        <Card key={`romlo-${breeder.name}`} className=\"w-full\">\n          <CardHeader>\n            <CardTitle className=\"flex items-center gap-3\">\n              <div\n                className=\"w-4 h-4 rounded-full\"\n                style={{ backgroundColor: breeder.color }}\n              />\n              {breeder.name} - Romló bogyó\n            </CardTitle>\n            <CardDescription>\n              {varieties.length} fajta adatai\n            </CardDescription>\n          </CardHeader>\n          <CardContent>\n            <div \n              onMouseEnter={() => setSelectedBreeder(breeder.name)}\n              onMouseLeave={() => setSelectedBreeder(null)}\n            >\n              <BreederChart\n                title=\"Romló bogyó mennyisége\"\n                varieties={varieties}\n                breederColor={breeder.color}\n                breederName={breeder.name}\n                allVarietiesData={romloData}\n              />\n            </div>\n          </CardContent>\n        </Card>\n      );\n    }\n  });\n\n  return (\n    <div className=\"min-h-screen bg-background p-6\">\n      <div className=\"max-w-[1920px] mx-auto space-y-8\">\n        {/* Header */}\n        <div className=\"text-center space-y-2\">\n          <h1 className=\"text-4xl font-bold text-foreground\">\n            Univer 2025 Dashboard\n          </h1>\n          <p className=\"text-muted-foreground text-lg\">\n            Tövön tarthatóság elemzés nemesítőházak szerint\n          </p>\n        </div>\n\n        {/* Dashboard Grid with all charts */}\n        <DashboardGrid>\n          {chartComponents}\n        </DashboardGrid>\n\n        {/* Insights Panel - shows for selected breeder */}\n        {selectedBreeder && (\n          <InsightsPanel \n            data={[...(erettGrouped[selectedBreeder] || []), ...(romloGrouped[selectedBreeder] || [])]}\n            breederName={selectedBreeder}\n          />\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;;;;AAEA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,sBAAsB;IACtB,MAAM,YAAY,IAAA,iJAAgB,EAAC;IACnC,MAAM,YAAY,IAAA,iJAAgB,EAAC;IAEnC,MAAM,eAAe,IAAA,mJAAkB,EAAC;IACxC,MAAM,eAAe,IAAA,mJAAkB,EAAC;IAExC,2BAA2B;IAC3B,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,IAAA,iNAAQ,EAAgB;IAEtE,kDAAkD;IAClD,MAAM,kBAAqC,EAAE;IAE7C,mBAAmB;IACnB,yIAAQ,CAAC,OAAO,CAAC,CAAC;QAChB,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;QAClD,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,gBAAgB,IAAI,eAClB,8OAAC,wIAAI;gBAA+B,WAAU;;kCAC5C,8OAAC,8IAAU;;0CACT,8OAAC,6IAAS;gCAAC,WAAU;;kDACnB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,QAAQ,KAAK;wCAAC;;;;;;oCAEzC,QAAQ,IAAI;oCAAC;;;;;;;0CAEhB,8OAAC,mJAAe;;oCACb,UAAU,MAAM;oCAAC;;;;;;;;;;;;;kCAGtB,8OAAC,+IAAW;kCACV,cAAA,8OAAC;4BACC,cAAc,IAAM,mBAAmB,QAAQ,IAAI;4BACnD,cAAc,IAAM,mBAAmB;sCAEvC,cAAA,8OAAC,6IAAY;gCACX,OAAM;gCACN,WAAW;gCACX,cAAc,QAAQ,KAAK;gCAC3B,aAAa,QAAQ,IAAI;gCACzB,kBAAkB;;;;;;;;;;;;;;;;;eAvBf,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;QA6BtC;IACF;IAEA,mBAAmB;IACnB,yIAAQ,CAAC,OAAO,CAAC,CAAC;QAChB,MAAM,YAAY,YAAY,CAAC,QAAQ,IAAI,CAAC,IAAI,EAAE;QAClD,IAAI,UAAU,MAAM,GAAG,GAAG;YACxB,gBAAgB,IAAI,eAClB,8OAAC,wIAAI;gBAA+B,WAAU;;kCAC5C,8OAAC,8IAAU;;0CACT,8OAAC,6IAAS;gCAAC,WAAU;;kDACnB,8OAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,iBAAiB,QAAQ,KAAK;wCAAC;;;;;;oCAEzC,QAAQ,IAAI;oCAAC;;;;;;;0CAEhB,8OAAC,mJAAe;;oCACb,UAAU,MAAM;oCAAC;;;;;;;;;;;;;kCAGtB,8OAAC,+IAAW;kCACV,cAAA,8OAAC;4BACC,cAAc,IAAM,mBAAmB,QAAQ,IAAI;4BACnD,cAAc,IAAM,mBAAmB;sCAEvC,cAAA,8OAAC,6IAAY;gCACX,OAAM;gCACN,WAAW;gCACX,cAAc,QAAQ,KAAK;gCAC3B,aAAa,QAAQ,IAAI;gCACzB,kBAAkB;;;;;;;;;;;;;;;;;eAvBf,CAAC,MAAM,EAAE,QAAQ,IAAI,EAAE;;;;;QA6BtC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAqC;;;;;;sCAGnD,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;;;;;;;8BAM/C,8OAAC;8BACE;;;;;;gBAIF,iCACC,8OAAC,8IAAa;oBACZ,MAAM;2BAAK,YAAY,CAAC,gBAAgB,IAAI,EAAE;2BAAO,YAAY,CAAC,gBAAgB,IAAI,EAAE;qBAAE;oBAC1F,aAAa;;;;;;;;;;;;;;;;;AAMzB", "debugId": null}}]}