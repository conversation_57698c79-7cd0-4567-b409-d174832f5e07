@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;

  /* ===== VILÁGOS MÓD (alapértelmezett) ===== */
  /* Természetes, meleg, napfényes színek - kiváló kontraszttal */
  --background: 255 255 255; /* Tiszta fehér háttér */
  --foreground: 15 23 42; /* Mély sötétkék szöveg - kiváló kontrasztú */
  --card: 248 250 252; /* Nagyon világos szürke kártyák */
  --card-foreground: 15 23 42; /* Mély sötétkék szöveg */
  --popover: 255 255 255; /* Fehér popup háttér */
  --popover-foreground: 15 23 42; /* Mély sötétkék szöveg */
  --primary: 22 163 74; /* Élénk zöld - paradicsom levelek */
  --primary-foreground: 255 255 255; /* Fehér szöveg zöld háttéren */
  --secondary: 241 245 249; /* Világos szürke */
  --secondary-foreground: 15 23 42; /* Mély sötétkék szöveg */
  --muted: 248 250 252; /* Nagyon világos szürke */
  --muted-foreground: 71 85 105; /* Közepes szürke - jó kontrasztú */
  --accent: 220 38 38; /* Élénk piros - paradicsom */
  --accent-foreground: 255 255 255; /* Fehér szöveg piros háttéren */
  --destructive: 239 68 68; /* Piros figyelmeztetés */
  --destructive-foreground: 255 255 255; /* Fehér szöveg */
  --border: 226 232 240; /* Világos szürke keret */
  --input: 255 255 255; /* Fehér input háttér */
  --ring: 22 163 74; /* Zöld focus ring */
  --chart-1: 22 163 74; /* Zöld */
  --chart-2: 220 38 38; /* Piros */
  --chart-3: 245 158 11; /* Narancs */
  --chart-4: 147 51 234; /* Lila */
  --chart-5: 59 130 246; /* Kék */
  --sidebar: 248 250 252;
  --sidebar-foreground: 15 23 42;
  --sidebar-primary: 22 163 74;
  --sidebar-primary-foreground: 255 255 255;
  --sidebar-accent: 241 245 249;
  --sidebar-accent-foreground: 15 23 42;
  --sidebar-border: 226 232 240;
  --sidebar-ring: 22 163 74;
}

.dark {
  /* ===== SÖTÉT MÓD ===== */
  /* Mély, természetes éjszakai színek - kiváló kontraszttal */
  --background: 15 23 42; /* Mély éjkék háttér */
  --foreground: 248 250 252; /* Tiszta fehér szöveg - kiváló kontrasztú */
  --card: 30 41 59; /* Sötétebb kék kártyák */
  --card-foreground: 248 250 252; /* Tiszta fehér szöveg */
  --popover: 30 41 59; /* Sötét popup háttér */
  --popover-foreground: 248 250 252; /* Tiszta fehér szöveg */
  --primary: 34 197 94; /* Élénk zöld - paradicsom levelek */
  --primary-foreground: 15 23 42; /* Sötét szöveg zöld háttéren */
  --secondary: 51 65 85; /* Közepes sötét szürke */
  --secondary-foreground: 248 250 252; /* Tiszta fehér szöveg */
  --muted: 51 65 85; /* Közepes sötét szürke */
  --muted-foreground: 148 163 184; /* Világos szürke - jó kontrasztú */
  --accent: 239 68 68; /* Élénk piros - paradicsom */
  --accent-foreground: 248 250 252; /* Tiszta fehér szöveg */
  --destructive: 239 68 68; /* Piros figyelmeztetés */
  --destructive-foreground: 248 250 252; /* Tiszta fehér szöveg */
  --border: 71 85 105; /* Közepes szürke keret */
  --input: 51 65 85; /* Sötét input háttér */
  --ring: 34 197 94; /* Zöld focus ring */
  --chart-1: 34 197 94; /* Zöld */
  --chart-2: 239 68 68; /* Piros */
  --chart-3: 245 158 11; /* Narancs */
  --chart-4: 168 85 247; /* Lila */
  --chart-5: 59 130 246; /* Kék */
  --sidebar: 30 41 59;
  --sidebar-foreground: 248 250 252;
  --sidebar-primary: 34 197 94;
  --sidebar-primary-foreground: 15 23 42;
  --sidebar-accent: 51 65 85;
  --sidebar-accent-foreground: 248 250 252;
  --sidebar-border: 71 85 105;
  --sidebar-ring: 34 197 94;
}

/* ===== ALAPVETŐ STÍLUSOK ===== */
body {
  font-family: var(--font-geist-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.2s ease, color 0.2s ease;
}

/* Sötét mód beállítások */
.dark {
  color-scheme: dark;
}

/* Világos mód beállítások */
:root:not(.dark) {
  color-scheme: light;
}

/* ===== GLOBÁLIS ÁTMENETEK ===== */
/* Sima átmenetek minden elemre - teljesítmény optimalizált */
*,
*::before,
*::after {
  transition: background-color 0.2s ease,
              color 0.2s ease,
              border-color 0.2s ease,
              box-shadow 0.2s ease;
}

/* ===== HOZZÁFÉRHETŐSÉG ===== */
/* Focus láthatóság javítása */
:focus-visible {
  outline: 2px solid rgb(var(--ring));
  outline-offset: 2px;
  border-radius: 4px;
}

/* Csökkentett mozgás preferencia esetén */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
