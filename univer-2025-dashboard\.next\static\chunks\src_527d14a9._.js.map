{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/components/ColumnChart.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport Highcharts from 'highcharts';\r\nimport HighchartsReact from 'highcharts-react-official';\r\n\r\ninterface ColumnChartProps {\r\n  title?: string;\r\n  data?: Array<{ name: string; y: number }>;\r\n}\r\n\r\ninterface SelectedDataPoint {\r\n  name: string;\r\n  value: number;\r\n  index: number;\r\n}\r\n\r\n// Információs panel komponens\r\nconst DataInfoPanel: React.FC<{ selectedData: SelectedDataPoint | null; allData: Array<{ name: string; y: number }> }> = ({ selectedData, allData }) => {\r\n  if (!selectedData) {\r\n    return (\r\n      <div className=\"w-80 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ml-4 transition-all duration-300\">\r\n        <div className=\"text-center\">\r\n          <div className=\"w-16 h-16 mx-auto mb-4 bg-gray-700 rounded-full flex items-center justify-center\">\r\n            <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n            </svg>\r\n          </div>\r\n          <h3 className=\"text-lg font-semibold text-white mb-2\">Adatok</h3>\r\n          <p className=\"text-gray-400 text-sm\">\r\n            Kattints egy oszlopra az adatok megtekintéséhez\r\n          </p>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  // Statisztikák számítása\r\n  const maxValue = Math.max(...allData.map(d => d.y));\r\n  const minValue = Math.min(...allData.map(d => d.y));\r\n  const avgValue = allData.reduce((sum, d) => sum + d.y, 0) / allData.length;\r\n  const percentageOfMax = (selectedData.value / maxValue) * 100;\r\n  const isAboveAverage = selectedData.value > avgValue;\r\n\r\n  return (\r\n    <div className=\"w-80 bg-gray-800/50 backdrop-blur-sm border border-gray-700 rounded-lg p-6 ml-4 transition-all duration-300\">\r\n      {/* Fejléc */}\r\n      <div className=\"flex items-center mb-4\">\r\n        <div className=\"w-10 h-10 bg-gradient-to-r from-purple-500 to-indigo-500 rounded-full flex items-center justify-center mr-3\">\r\n          <svg className=\"w-5 h-5 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\r\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\r\n          </svg>\r\n        </div>\r\n        <div>\r\n          <h3 className=\"text-lg font-semibold text-white\">Kiválasztott adat</h3>\r\n          <p className=\"text-xs text-gray-400\">#{selectedData.index + 1} pozíció</p>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Fő adatok */}\r\n      <div className=\"space-y-4\">\r\n        <div className=\"bg-gray-700/50 rounded-lg p-4\">\r\n          <div className=\"flex justify-between items-center mb-2\">\r\n            <span className=\"text-gray-300 text-sm\">Kategória</span>\r\n            <span className=\"text-white font-semibold\">{selectedData.name}</span>\r\n          </div>\r\n          <div className=\"flex justify-between items-center\">\r\n            <span className=\"text-gray-300 text-sm\">Érték</span>\r\n            <span className=\"text-2xl font-bold text-white\">{selectedData.value.toFixed(1)}</span>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Vizuális progress bar */}\r\n        <div className=\"space-y-2\">\r\n          <div className=\"flex justify-between text-xs text-gray-400\">\r\n            <span>Relatív érték</span>\r\n            <span>{percentageOfMax.toFixed(1)}% a maximumból</span>\r\n          </div>\r\n          <div className=\"w-full bg-gray-700 rounded-full h-3\">\r\n            <div\r\n              className=\"bg-gradient-to-r from-purple-500 to-indigo-500 h-3 rounded-full transition-all duration-500 ease-out\"\r\n              style={{ width: `${percentageOfMax}%` }}\r\n            ></div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Statisztikák */}\r\n        <div className=\"grid grid-cols-2 gap-3\">\r\n          <div className=\"bg-gray-700/30 rounded-lg p-3 text-center\">\r\n            <div className=\"text-xs text-gray-400 mb-1\">Maximum</div>\r\n            <div className=\"text-sm font-semibold text-white\">{maxValue.toFixed(1)}</div>\r\n          </div>\r\n          <div className=\"bg-gray-700/30 rounded-lg p-3 text-center\">\r\n            <div className=\"text-xs text-gray-400 mb-1\">Átlag</div>\r\n            <div className=\"text-sm font-semibold text-white\">{avgValue.toFixed(1)}</div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Összehasonlítás */}\r\n        <div className={`rounded-lg p-3 border ${isAboveAverage ? 'bg-green-900/20 border-green-500/30' : 'bg-orange-900/20 border-orange-500/30'}`}>\r\n          <div className=\"flex items-center\">\r\n            <div className={`w-2 h-2 rounded-full mr-2 ${isAboveAverage ? 'bg-green-400' : 'bg-orange-400'}`}></div>\r\n            <span className={`text-xs ${isAboveAverage ? 'text-green-300' : 'text-orange-300'}`}>\r\n              {isAboveAverage ? 'Átlag feletti érték' : 'Átlag alatti érték'}\r\n            </span>\r\n          </div>\r\n          <div className={`text-xs mt-1 ${isAboveAverage ? 'text-green-400' : 'text-orange-400'}`}>\r\n            {isAboveAverage ? '+' : ''}{(selectedData.value - avgValue).toFixed(1)} az átlagtól\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst ColumnChart: React.FC<ColumnChartProps> = ({\r\n  title = \"Oszlop Diagram\",\r\n  data = [\r\n    { name: 'Január', y: 29.9 },\r\n    { name: 'Február', y: 71.5 },\r\n    { name: 'Március', y: 106.4 },\r\n    { name: 'Április', y: 129.2 },\r\n    { name: 'Május', y: 144.0 },\r\n    { name: 'Június', y: 176.0 }\r\n  ]\r\n}) => {\r\n  const chartRef = React.useRef<HTMLDivElement>(null);\r\n  const [selectedData, setSelectedData] = useState<SelectedDataPoint | null>(null);\r\n  const options: Highcharts.Options = {\r\n    chart: {\r\n      type: 'column',\r\n      backgroundColor: 'transparent',\r\n      style: {\r\n        fontFamily: 'var(--font-geist-sans)'\r\n      }\r\n    },\r\n    tooltip: {\r\n      enabled: false\r\n    },\r\n    title: {\r\n      text: title,\r\n      style: {\r\n        color: '#ffffff',\r\n        fontSize: '20px',\r\n        fontWeight: '600'\r\n      }\r\n    },\r\n    xAxis: {\r\n      type: 'category',\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      lineColor: '#3f3f46',\r\n      tickColor: '#3f3f46'\r\n    },\r\n    yAxis: {\r\n      title: {\r\n        text: 'Értékek',\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      labels: {\r\n        style: {\r\n          color: '#a1a1aa'\r\n        }\r\n      },\r\n      gridLineColor: '#3f3f46'\r\n    },\r\n    legend: {\r\n      enabled: false\r\n    },\r\n    plotOptions: {\r\n      column: {\r\n        borderWidth: 0,\r\n        borderRadius: 4,\r\n        color: {\r\n          linearGradient: { x1: 0, y1: 0, x2: 0, y2: 1 },\r\n          stops: [\r\n            [0, '#8b5cf6'],\r\n            [1, '#6366f1']\r\n          ]\r\n        },\r\n        dataLabels: {\r\n          enabled: true,\r\n          style: {\r\n            color: '#ffffff',\r\n            textOutline: 'none'\r\n          }\r\n        },\r\n        point: {\r\n          events: {\r\n            click: function(this: Highcharts.Point) {\r\n              const pointData = this as any;\r\n              setSelectedData({\r\n                name: pointData.name || pointData.category,\r\n                value: pointData.y,\r\n                index: pointData.index\r\n              });\r\n            }\r\n          }\r\n        },\r\n        cursor: 'pointer',\r\n        states: {\r\n          hover: {\r\n            brightness: 0.1\r\n          },\r\n          select: {\r\n            brightness: 0.2,\r\n            borderColor: '#ffffff',\r\n            borderWidth: 2\r\n          }\r\n        }\r\n      }\r\n    },\r\n    series: [{\r\n      type: 'column',\r\n      name: 'Adatok',\r\n      data: data\r\n    }],\r\n    exporting: {\r\n      enabled: true,\r\n      buttons: {\r\n        contextButton: {\r\n          enabled: true,\r\n          theme: {\r\n            fill: 'rgba(55, 65, 81, 0.9)',\r\n            stroke: '#ffffff',\r\n            r: 4,\r\n            states: {\r\n              hover: {\r\n                fill: 'rgba(75, 85, 99, 0.95)',\r\n                stroke: '#ffffff'\r\n              },\r\n              select: {\r\n                fill: 'rgba(107, 114, 128, 0.95)',\r\n                stroke: '#ffffff'\r\n              }\r\n            }\r\n          } as any,\r\n          menuItems: [\r\n            'viewFullscreen',\r\n            'separator',\r\n            'downloadPNG',\r\n            'downloadJPEG',\r\n            'downloadSVG'\r\n          ],\r\n          x: -10,\r\n          y: 10\r\n        }\r\n      }\r\n    },\r\n    navigation: {\r\n      buttonOptions: {\r\n        enabled: true\r\n      }\r\n    },\r\n  };\r\n\r\n  return (\r\n    <div className=\"w-full flex gap-4\">\r\n      <div className=\"flex-1 h-96 relative\">\r\n        <button\r\n          onClick={() => {\r\n            if (chartRef.current) {\r\n              if (chartRef.current.requestFullscreen) {\r\n                chartRef.current.requestFullscreen();\r\n              } else if ((chartRef.current as any).webkitRequestFullscreen) {\r\n                (chartRef.current as any).webkitRequestFullscreen();\r\n              } else if ((chartRef.current as any).mozRequestFullScreen) {\r\n                (chartRef.current as any).mozRequestFullScreen();\r\n              } else if ((chartRef.current as any).msRequestFullscreen) {\r\n                (chartRef.current as any).msRequestFullscreen();\r\n              }\r\n            }\r\n          }}\r\n          className=\"absolute top-2 right-2 z-10 bg-gray-700 hover:bg-gray-600 text-white p-2 rounded-md transition-colors duration-200 shadow-lg\"\r\n          title=\"Teljes képernyő\"\r\n        >\r\n          <svg\r\n            className=\"w-5 h-5\"\r\n            fill=\"none\"\r\n            stroke=\"currentColor\"\r\n            viewBox=\"0 0 24 24\"\r\n            xmlns=\"http://www.w3.org/2000/svg\"\r\n          >\r\n            <path\r\n              strokeLinecap=\"round\"\r\n              strokeLinejoin=\"round\"\r\n              strokeWidth={2}\r\n              d=\"M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4\"\r\n            />\r\n          </svg>\r\n        </button>\r\n        <div ref={chartRef} className=\"w-full h-96\">\r\n          <HighchartsReact\r\n            highcharts={Highcharts}\r\n            options={options}\r\n          />\r\n        </div>\r\n      </div>\r\n      <DataInfoPanel selectedData={selectedData} allData={data} />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ColumnChart;\r\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBA,8BAA8B;AAC9B,MAAM,gBAAmH;QAAC,EAAE,YAAY,EAAE,OAAO,EAAE;IACjJ,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,yBAAyB;IACzB,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,CAAC;IACjD,MAAM,WAAW,KAAK,GAAG,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,CAAC;IACjD,MAAM,WAAW,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,MAAM;IAC1E,MAAM,kBAAkB,AAAC,aAAa,KAAK,GAAG,WAAY;IAC1D,MAAM,iBAAiB,aAAa,KAAK,GAAG;IAE5C,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;4BAAqB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC5E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;kCAGzE,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;;oCAAwB;oCAAE,aAAa,KAAK,GAAG;oCAAE;;;;;;;;;;;;;;;;;;;0BAKlE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDAA4B,aAAa,IAAI;;;;;;;;;;;;0CAE/D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAAwB;;;;;;kDACxC,6LAAC;wCAAK,WAAU;kDAAiC,aAAa,KAAK,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;kCAKhF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDAAK;;;;;;kDACN,6LAAC;;4CAAM,gBAAgB,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAEpC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,AAAC,GAAkB,OAAhB,iBAAgB;oCAAG;;;;;;;;;;;;;;;;;kCAM5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAoC,SAAS,OAAO,CAAC;;;;;;;;;;;;0CAEtE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAA6B;;;;;;kDAC5C,6LAAC;wCAAI,WAAU;kDAAoC,SAAS,OAAO,CAAC;;;;;;;;;;;;;;;;;;kCAKxE,6LAAC;wBAAI,WAAW,AAAC,yBAAyH,OAAjG,iBAAiB,wCAAwC;;0CAChG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAW,AAAC,6BAA8E,OAAlD,iBAAiB,iBAAiB;;;;;;kDAC/E,6LAAC;wCAAK,WAAW,AAAC,WAAgE,OAAtD,iBAAiB,mBAAmB;kDAC7D,iBAAiB,wBAAwB;;;;;;;;;;;;0CAG9C,6LAAC;gCAAI,WAAW,AAAC,gBAAqE,OAAtD,iBAAiB,mBAAmB;;oCACjE,iBAAiB,MAAM;oCAAI,CAAC,aAAa,KAAK,GAAG,QAAQ,EAAE,OAAO,CAAC;oCAAG;;;;;;;;;;;;;;;;;;;;;;;;;AAMnF;KA/FM;AAiGN,MAAM,cAA0C;QAAC,EAC/C,QAAQ,gBAAgB,EACxB,OAAO;QACL;YAAE,MAAM;YAAU,GAAG;QAAK;QAC1B;YAAE,MAAM;YAAW,GAAG;QAAK;QAC3B;YAAE,MAAM;YAAW,GAAG;QAAM;QAC5B;YAAE,MAAM;YAAW,GAAG;QAAM;QAC5B;YAAE,MAAM;YAAS,GAAG;QAAM;QAC1B;YAAE,MAAM;YAAU,GAAG;QAAM;KAC5B,EACF;;IACC,MAAM,WAAW,wKAAK,CAAC,MAAM,CAAiB;IAC9C,MAAM,CAAC,cAAc,gBAAgB,GAAG,IAAA,yKAAQ,EAA2B;IAC3E,MAAM,UAA8B;QAClC,OAAO;YACL,MAAM;YACN,iBAAiB;YACjB,OAAO;gBACL,YAAY;YACd;QACF;QACA,SAAS;YACP,SAAS;QACX;QACA,OAAO;YACL,MAAM;YACN,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,YAAY;YACd;QACF;QACA,OAAO;YACL,MAAM;YACN,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,OAAO;gBACL,MAAM;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,QAAQ;gBACN,OAAO;oBACL,OAAO;gBACT;YACF;YACA,eAAe;QACjB;QACA,QAAQ;YACN,SAAS;QACX;QACA,aAAa;YACX,QAAQ;gBACN,aAAa;gBACb,cAAc;gBACd,OAAO;oBACL,gBAAgB;wBAAE,IAAI;wBAAG,IAAI;wBAAG,IAAI;wBAAG,IAAI;oBAAE;oBAC7C,OAAO;wBACL;4BAAC;4BAAG;yBAAU;wBACd;4BAAC;4BAAG;yBAAU;qBACf;gBACH;gBACA,YAAY;oBACV,SAAS;oBACT,OAAO;wBACL,OAAO;wBACP,aAAa;oBACf;gBACF;gBACA,OAAO;oBACL,QAAQ;wBACN,OAAO;4BACL,MAAM,YAAY,IAAI;4BACtB,gBAAgB;gCACd,MAAM,UAAU,IAAI,IAAI,UAAU,QAAQ;gCAC1C,OAAO,UAAU,CAAC;gCAClB,OAAO,UAAU,KAAK;4BACxB;wBACF;oBACF;gBACF;gBACA,QAAQ;gBACR,QAAQ;oBACN,OAAO;wBACL,YAAY;oBACd;oBACA,QAAQ;wBACN,YAAY;wBACZ,aAAa;wBACb,aAAa;oBACf;gBACF;YACF;QACF;QACA,QAAQ;YAAC;gBACP,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SAAE;QACF,WAAW;YACT,SAAS;YACT,SAAS;gBACP,eAAe;oBACb,SAAS;oBACT,OAAO;wBACL,MAAM;wBACN,QAAQ;wBACR,GAAG;wBACH,QAAQ;4BACN,OAAO;gCACL,MAAM;gCACN,QAAQ;4BACV;4BACA,QAAQ;gCACN,MAAM;gCACN,QAAQ;4BACV;wBACF;oBACF;oBACA,WAAW;wBACT;wBACA;wBACA;wBACA;wBACA;qBACD;oBACD,GAAG,CAAC;oBACJ,GAAG;gBACL;YACF;QACF;QACA,YAAY;YACV,eAAe;gBACb,SAAS;YACX;QACF;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,SAAS;4BACP,IAAI,SAAS,OAAO,EAAE;gCACpB,IAAI,SAAS,OAAO,CAAC,iBAAiB,EAAE;oCACtC,SAAS,OAAO,CAAC,iBAAiB;gCACpC,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,uBAAuB,EAAE;oCAC3D,SAAS,OAAO,CAAS,uBAAuB;gCACnD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,oBAAoB,EAAE;oCACxD,SAAS,OAAO,CAAS,oBAAoB;gCAChD,OAAO,IAAI,AAAC,SAAS,OAAO,CAAS,mBAAmB,EAAE;oCACvD,SAAS,OAAO,CAAS,mBAAmB;gCAC/C;4BACF;wBACF;wBACA,WAAU;wBACV,OAAM;kCAEN,cAAA,6LAAC;4BACC,WAAU;4BACV,MAAK;4BACL,QAAO;4BACP,SAAQ;4BACR,OAAM;sCAEN,cAAA,6LAAC;gCACC,eAAc;gCACd,gBAAe;gCACf,aAAa;gCACb,GAAE;;;;;;;;;;;;;;;;kCAIR,6LAAC;wBAAI,KAAK;wBAAU,WAAU;kCAC5B,cAAA,6LAAC,mMAAe;4BACd,YAAY,sJAAU;4BACtB,SAAS;;;;;;;;;;;;;;;;;0BAIf,6LAAC;gBAAc,cAAc;gBAAc,SAAS;;;;;;;;;;;;AAG1D;GA/LM;MAAA;uCAiMS", "debugId": null}}, {"offset": {"line": 660, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Dr%C3%B3n/inputs_2025/varianca_analizis/Szed%C3%A9sek/app_univer/univer-2025-dashboard/src/app/test-column-chart/page.tsx"], "sourcesContent": ["'use client';\n\nimport ColumnChart from \"@/components/ColumnChart\";\n\nexport default function TestColumnChart() {\n  const testData = [\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 29.9 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 71.5 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 106.4 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 129.2 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 144.0 },\n    { name: '<PERSON><PERSON><PERSON>', y: 176.0 },\n    { name: '<PERSON><PERSON><PERSON>', y: 135.6 },\n    { name: '<PERSON><PERSON><PERSON><PERSON>', y: 148.5 }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-900 p-6\">\n      <div className=\"max-w-7xl mx-auto space-y-8\">\n        {/* Header */}\n        <div className=\"text-center space-y-2\">\n          <h1 className=\"text-3xl font-bold text-white\">\n            ColumnChart Teszt\n          </h1>\n          <p className=\"text-gray-400\">\n            Kattints egy oszlopra az adatok megtekintéséhez\n          </p>\n        </div>\n\n        {/* Chart */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <ColumnChart \n            title=\"Havi Értékek Tesztje\"\n            data={testData}\n          />\n        </div>\n\n        {/* Instrukciók */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-white mb-4\">Tesztelési útmutató:</h2>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n            <div>\n              <h3 className=\"text-lg font-medium text-white mb-3\">Funkciók:</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• <span className=\"text-green-400\">Kattintás:</span> Oszlopra kattintva megjelenik az információs panel</li>\n                <li>• <span className=\"text-blue-400\">Tooltip letiltva:</span> Nincs hover tooltip</li>\n                <li>• <span className=\"text-purple-400\">Responsív layout:</span> A diagram és panel egymás mellett</li>\n                <li>• <span className=\"text-yellow-400\">Animációk:</span> Smooth átmenetek és vizuális visszajelzések</li>\n              </ul>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-medium text-white mb-3\">Információs panel tartalma:</h3>\n              <ul className=\"text-gray-300 space-y-2\">\n                <li>• <span className=\"text-green-400\">Alapadatok:</span> Kategória, érték, pozíció</li>\n                <li>• <span className=\"text-blue-400\">Vizuális progress:</span> Relatív érték megjelenítése</li>\n                <li>• <span className=\"text-purple-400\">Statisztikák:</span> Maximum, átlag értékek</li>\n                <li>• <span className=\"text-yellow-400\">Összehasonlítás:</span> Átlag feletti/alatti jelzés</li>\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* Második teszt diagram */}\n        <div className=\"bg-gray-800 rounded-lg p-6\">\n          <h2 className=\"text-xl font-semibold text-white mb-4\">Második teszt - Különböző adatok</h2>\n          <ColumnChart\n            title=\"Negyedéves Teljesítmény\"\n            data={[\n              { name: 'Q1', y: 85.2 },\n              { name: 'Q2', y: 92.7 },\n              { name: 'Q3', y: 78.9 },\n              { name: 'Q4', y: 105.3 }\n            ]}\n          />\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIe,SAAS;IACtB,MAAM,WAAW;QACf;YAAE,MAAM;YAAU,GAAG;QAAK;QAC1B;YAAE,MAAM;YAAW,GAAG;QAAK;QAC3B;YAAE,MAAM;YAAW,GAAG;QAAM;QAC5B;YAAE,MAAM;YAAW,GAAG;QAAM;QAC5B;YAAE,MAAM;YAAS,GAAG;QAAM;QAC1B;YAAE,MAAM;YAAU,GAAG;QAAM;QAC3B;YAAE,MAAM;YAAU,GAAG;QAAM;QAC3B;YAAE,MAAM;YAAa,GAAG;QAAM;KAC/B;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAgC;;;;;;sCAG9C,6LAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;8BAM/B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,+IAAW;wBACV,OAAM;wBACN,MAAM;;;;;;;;;;;8BAKV,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;wDAAiB;;;;;;;8DACxD,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAwB;;;;;;;8DAC9D,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAkB;;;;;;wDAAwB;;;;;;;8DAChE,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAkB;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;8CAG7D,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAsC;;;;;;sDACpD,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAiB;;;;;;wDAAkB;;;;;;;8DACzD,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAAyB;;;;;;;8DAC/D,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAkB;;;;;;wDAAoB;;;;;;;8DAC5D,6LAAC;;wDAAG;sEAAE,6LAAC;4DAAK,WAAU;sEAAkB;;;;;;wDAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOvE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAAwC;;;;;;sCACtD,6LAAC,+IAAW;4BACV,OAAM;4BACN,MAAM;gCACJ;oCAAE,MAAM;oCAAM,GAAG;gCAAK;gCACtB;oCAAE,MAAM;oCAAM,GAAG;gCAAK;gCACtB;oCAAE,MAAM;oCAAM,GAAG;gCAAK;gCACtB;oCAAE,MAAM;oCAAM,GAAG;gCAAM;6BACxB;;;;;;;;;;;;;;;;;;;;;;;AAMb;KA1EwB", "debugId": null}}]}